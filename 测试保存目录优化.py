#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保存和加载结果的默认目录优化
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_save_load_directory_optimization():
    """测试保存和加载的默认目录优化"""
    print("测试保存和加载结果的默认目录优化")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="save_dir_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试1：验证默认目录逻辑
        print(f"\n=== 测试1: 验证默认目录逻辑 ===")
        
        # 情况1：没有设置current_directory
        app.current_directory = ""
        
        # 模拟获取保存默认目录的逻辑
        initial_dir = app.current_directory if app.current_directory and os.path.exists(app.current_directory) else os.path.expanduser("~")
        home_dir = os.path.expanduser("~")
        
        print(f"当前目录为空时:")
        print(f"  current_directory: '{app.current_directory}'")
        print(f"  默认目录: {initial_dir}")
        print(f"  用户主目录: {home_dir}")
        
        if initial_dir == home_dir:
            print("  ✅ 正确回退到用户主目录")
        else:
            print("  ❌ 默认目录逻辑错误")
        
        # 情况2：设置了有效的current_directory
        app.current_directory = test_dir
        
        initial_dir = app.current_directory if app.current_directory and os.path.exists(app.current_directory) else os.path.expanduser("~")
        
        print(f"\n设置有效目录时:")
        print(f"  current_directory: {app.current_directory}")
        print(f"  默认目录: {initial_dir}")
        
        if initial_dir == test_dir:
            print("  ✅ 正确使用当前选择的目录")
        else:
            print("  ❌ 未使用当前选择的目录")
        
        # 情况3：设置了无效的current_directory
        app.current_directory = "/nonexistent/directory"
        
        initial_dir = app.current_directory if app.current_directory and os.path.exists(app.current_directory) else os.path.expanduser("~")
        
        print(f"\n设置无效目录时:")
        print(f"  current_directory: {app.current_directory}")
        print(f"  默认目录: {initial_dir}")
        
        if initial_dir == home_dir:
            print("  ✅ 正确回退到用户主目录")
        else:
            print("  ❌ 未正确处理无效目录")
        
        # 测试2：模拟实际使用场景
        print(f"\n=== 测试2: 模拟实际使用场景 ===")
        
        # 创建一些测试文件
        test_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"test_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * 1024)
            test_files.append(file_path)
        
        # 设置应用状态
        app.current_directory = test_dir
        app.current_results = {
            1024: test_files
        }
        
        print(f"模拟用户场景:")
        print(f"  用户选择的文件夹: {app.current_directory}")
        print(f"  搜索结果: {len(app.current_results)} 个组")
        
        # 验证保存时的默认目录
        save_initial_dir = app.current_directory if app.current_directory and os.path.exists(app.current_directory) else os.path.expanduser("~")
        print(f"  保存时默认目录: {save_initial_dir}")
        
        if save_initial_dir == test_dir:
            print("  ✅ 保存时正确使用用户选择的文件夹")
        else:
            print("  ❌ 保存时未使用用户选择的文件夹")
        
        # 验证加载时的默认目录
        load_initial_dir = app.current_directory if app.current_directory and os.path.exists(app.current_directory) else os.path.expanduser("~")
        print(f"  加载时默认目录: {load_initial_dir}")
        
        if load_initial_dir == test_dir:
            print("  ✅ 加载时正确使用用户选择的文件夹")
        else:
            print("  ❌ 加载时未使用用户选择的文件夹")
        
        # 测试3：测试目录变化的影响
        print(f"\n=== 测试3: 测试目录变化的影响 ===")
        
        # 创建另一个测试目录
        test_dir2 = tempfile.mkdtemp(prefix="save_dir_test2_")
        print(f"创建第二个测试目录: {test_dir2}")
        
        # 模拟用户切换到新目录
        app.current_directory = test_dir2
        
        # 验证默认目录是否跟随变化
        new_initial_dir = app.current_directory if app.current_directory and os.path.exists(app.current_directory) else os.path.expanduser("~")
        
        print(f"切换目录后:")
        print(f"  新的current_directory: {app.current_directory}")
        print(f"  新的默认目录: {new_initial_dir}")
        
        if new_initial_dir == test_dir2:
            print("  ✅ 默认目录正确跟随用户选择变化")
        else:
            print("  ❌ 默认目录未跟随用户选择变化")
        
        # 清理第二个测试目录
        try:
            shutil.rmtree(test_dir2)
        except:
            pass
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试各种边界情况
        test_cases = [
            ("空字符串", ""),
            ("None值", None),
            ("不存在的路径", "/this/path/does/not/exist"),
            ("文件路径而非目录", __file__),  # 当前脚本文件
            ("相对路径", "."),
            ("用户主目录", os.path.expanduser("~")),
        ]
        
        home_dir = os.path.expanduser("~")
        
        for case_name, test_path in test_cases:
            app.current_directory = test_path
            
            # 应用默认目录逻辑
            initial_dir = app.current_directory if app.current_directory and os.path.exists(app.current_directory) else home_dir
            
            print(f"{case_name}:")
            print(f"  输入: {test_path}")
            print(f"  结果: {initial_dir}")
            
            # 验证结果是否合理
            if os.path.exists(initial_dir) and os.path.isdir(initial_dir):
                print(f"  ✅ 结果是有效目录")
            else:
                print(f"  ❌ 结果不是有效目录")
        
        root.destroy()
        
    except Exception as e:
        print(f"边界测试出错: {str(e)}")

def demonstrate_user_experience():
    """演示用户体验改进"""
    print("\n" + "=" * 50)
    print("用户体验改进演示")
    print("=" * 50)
    
    print("🎯 优化前的用户体验:")
    print("1. 用户在 D:\\Videos 文件夹中搜索重复视频")
    print("2. 找到重复文件，想要保存结果")
    print("3. 点击'保存结果'按钮")
    print("4. ❌ 文件对话框打开在用户主目录（如 C:\\Users\\<USER>\\Videos 文件夹")
    print("6. 选择保存位置和文件名")
    
    print("\n🚀 优化后的用户体验:")
    print("1. 用户在 D:\\Videos 文件夹中搜索重复视频")
    print("2. 找到重复文件，想要保存结果")
    print("3. 点击'保存结果'按钮")
    print("4. ✅ 文件对话框直接打开在 D:\\Videos 文件夹")
    print("5. 用户直接输入文件名即可保存")
    print("6. 节省了导航时间，提高了效率")
    
    print("\n💡 同样的改进也适用于'加载结果'功能:")
    print("- 加载对话框也会在当前工作文件夹中打开")
    print("- 用户更容易找到之前保存的结果文件")
    print("- 保持了操作的一致性和连贯性")
    
    print("\n🛡️ 健壮性保障:")
    print("- 如果当前目录不存在，自动回退到用户主目录")
    print("- 如果当前目录为空，使用用户主目录")
    print("- 确保文件对话框始终能正常打开")

if __name__ == "__main__":
    test_save_load_directory_optimization()
    test_edge_cases()
    demonstrate_user_experience()
    
    print("\n" + "=" * 50)
    print("🎉 保存目录优化测试完成")
    print("=" * 50)
    
    print("\n📊 优化内容:")
    print("✅ 保存结果默认目录优化")
    print("   - 使用当前选择的文件夹作为默认保存目录")
    print("   - 无效目录时自动回退到用户主目录")
    
    print("\n✅ 加载结果默认目录优化")
    print("   - 使用当前选择的文件夹作为默认加载目录")
    print("   - 保持与保存功能的一致性")
    
    print("\n✅ 健壮性改进")
    print("   - 处理空目录、无效目录等边界情况")
    print("   - 确保文件对话框始终能正常工作")
    
    print("\n💡 用户体验提升:")
    print("- 减少用户的导航操作")
    print("- 提高保存和加载的效率")
    print("- 保持操作的直观性和一致性")
    print("- 符合用户的使用习惯和预期")
    
    print("\n🎯 适用场景:")
    print("- 用户在特定文件夹中搜索重复文件")
    print("- 需要在同一位置保存和加载结果")
    print("- 频繁的保存和加载操作")
    print("- 提高工作流程的连贯性")
