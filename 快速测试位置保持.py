#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试组删除后的位置保持功能
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_test_position_preservation():
    """快速测试位置保持功能"""
    print("快速测试组删除后的位置保持功能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="quick_position_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        all_files = []
        # 创建10个组，每组2个文件
        for i in range(10):
            size_kb = (i + 1) * 2  # 2KB, 4KB, 6KB, ...
            for j in range(2):
                file_path = os.path.join(test_dir, f"group{i:02d}_file{j}.jpg")
                with open(file_path, 'w') as f:
                    f.write('A' * (size_kb * 1024))
                all_files.append(file_path)
        
        print(f"创建了 {len(all_files)} 个测试文件，10个组")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        # 显示结果
        app.display_results(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 滚动到中间位置
        app.text_area.yview_moveto(0.5)
        root.update()
        time.sleep(0.1)
        
        # 记录删除前的位置
        before_position = app.text_area.yview()
        print(f"删除前滚动位置: {before_position[0]:.3f}")
        
        # 选择第一个组进行删除（删除1个文件会导致组删除）
        first_size = next(iter(app.current_results.keys()))
        first_files = app.current_results[first_size]
        file_to_delete = first_files[0]
        
        print(f"删除文件: {os.path.basename(file_to_delete)} (组大小: {first_size})")
        
        # 执行删除操作
        app.delete_file(file_to_delete, first_size)
        root.update()
        time.sleep(0.2)
        
        # 记录删除后的位置
        after_position = app.text_area.yview()
        print(f"删除后滚动位置: {after_position[0]:.3f}")
        
        # 计算位置差异
        position_diff = abs(after_position[0] - before_position[0])
        print(f"位置差异: {position_diff:.3f}")
        
        # 检查组是否被删除
        if first_size not in app.current_results:
            print("✅ 组已被正确删除")
            
            # 检查位置保持效果
            if position_diff < 0.1:  # 允许10%的位置差异
                print("🎉 组删除后位置保持成功！")
                return True
            else:
                print("❌ 组删除后位置发生了较大变化")
                return False
        else:
            print("❌ 组未被删除")
            return False
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            root.destroy()
        except:
            pass
        
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

if __name__ == "__main__":
    success = quick_test_position_preservation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 位置保持功能测试成功！")
        print("✅ 组删除后界面保持在原位置附近")
        print("✅ 解决了'列表回到最上面'的问题")
    else:
        print("⚠️ 位置保持功能仍需改进")
        print("❌ 组删除后界面位置发生了变化")
    
    print("\n💡 功能说明:")
    print("- 删除文件导致组删除时，程序会保存当前滚动位置")
    print("- 查找附近的文件作为定位点")
    print("- 结合保存的位置和焦点文件，保持界面在原位置附近")
    print("- 避免用户需要重新滚动定位的问题")
