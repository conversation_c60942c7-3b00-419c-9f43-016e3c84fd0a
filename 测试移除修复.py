#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移除操作的修复效果
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_single_file_removal():
    """测试单个文件移除时的组删除"""
    print("测试单个文件移除时的组删除")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="removal_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        # 组1：2个文件（移除1个后应该删除组）
        group1_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"group1_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * 1024)
            group1_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (1KB)")
        
        # 组2：3个文件（移除1个后应该保留组）
        group2_files = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"group2_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('B' * 2048)
            group2_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (2KB)")
        
        # 启动应用并搜索
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        app._search_by_size_worker()
        
        print(f"\n初始搜索结果:")
        print(f"找到 {len(app.current_results)} 个重复文件组")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 测试1：移除组1中的1个文件（应该删除整个组）
        print(f"\n=== 测试1: 移除组1中的1个文件 ===")
        if 1024 in app.current_results:
            file_to_remove = group1_files[0]
            print(f"移除文件: {os.path.basename(file_to_remove)}")
            
            # 执行移除操作
            app.remove_single_file(file_to_remove, 1024)
            
            print(f"移除后组数: {len(app.current_results)}")
            if 1024 in app.current_results:
                remaining = len(app.current_results[1024])
                print(f"  ❌ 组1仍存在，剩余 {remaining} 个文件（应该被删除）")
            else:
                print(f"  ✅ 组1已被正确删除")
        
        # 测试2：移除组2中的1个文件（应该保留组）
        print(f"\n=== 测试2: 移除组2中的1个文件 ===")
        if 2048 in app.current_results:
            file_to_remove = group2_files[0]
            print(f"移除文件: {os.path.basename(file_to_remove)}")
            
            # 执行移除操作
            app.remove_single_file(file_to_remove, 2048)
            
            print(f"移除后组数: {len(app.current_results)}")
            if 2048 in app.current_results:
                remaining = len(app.current_results[2048])
                print(f"  ✅ 组2正确保留，剩余 {remaining} 个文件")
            else:
                print(f"  ❌ 组2被错误删除")
        
        # 检查界面显示
        print(f"\n=== 界面显示检查 ===")
        content = app.text_area.get("1.0", tk.END)
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        group_lines = [line for line in lines if "文件大小:" in line]
        
        print(f"界面显示的组数: {len(group_lines)}")
        print(f"数据中的组数: {len(app.current_results)}")
        
        if len(group_lines) == len(app.current_results):
            print("✅ 界面显示与数据一致")
        else:
            print("❌ 界面显示与数据不一致")
            print("界面显示的组:")
            for line in group_lines:
                print(f"  {line}")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_batch_removal():
    """测试批量移除时的组删除"""
    print("\n" + "=" * 50)
    print("测试批量移除时的组删除")
    print("=" * 50)
    
    test_dir = tempfile.mkdtemp(prefix="batch_removal_test_")
    
    try:
        # 创建测试文件
        # 组1：2个文件（全部移除后应该删除组）
        group1_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"batch1_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('C' * 3072)
            group1_files.append(file_path)
        
        # 组2：3个文件（移除2个后应该删除组）
        group2_files = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"batch2_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('D' * 4096)
            group2_files.append(file_path)
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        app._search_by_size_worker()
        
        print(f"初始搜索结果: {len(app.current_results)} 个组")
        
        # 批量移除：组1的2个文件 + 组2的2个文件
        files_to_remove = group1_files + group2_files[:2]
        print(f"准备批量移除 {len(files_to_remove)} 个文件")
        
        # 模拟勾选文件
        for file_path in files_to_remove:
            if file_path not in app.checkbox_vars:
                app.checkbox_vars[file_path] = tk.BooleanVar()
            app.checkbox_vars[file_path].set(True)
        
        # 执行批量移除
        app.remove_selected_from_list()
        
        print(f"批量移除后组数: {len(app.current_results)}")
        
        # 检查结果
        if 3072 in app.current_results:
            print(f"  ❌ 组1仍存在（应该被删除）")
        else:
            print(f"  ✅ 组1已被正确删除")
        
        if 4096 in app.current_results:
            print(f"  ❌ 组2仍存在（应该被删除）")
        else:
            print(f"  ✅ 组2已被正确删除")
        
        root.destroy()
        
    except Exception as e:
        print(f"批量移除测试出错: {str(e)}")
    
    finally:
        try:
            shutil.rmtree(test_dir)
        except:
            pass

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试1：移除不存在的文件
        print("测试1: 移除不存在的文件")
        app.current_results = {1024: ["/test/file1.jpg", "/test/file2.jpg"]}
        
        try:
            app.remove_single_file("/test/nonexistent.jpg", 1024)
            print("✅ 移除不存在文件处理正常")
        except Exception as e:
            print(f"❌ 移除不存在文件出错: {str(e)}")
        
        # 测试2：移除不存在组中的文件
        print("\n测试2: 移除不存在组中的文件")
        try:
            app.remove_single_file("/test/file1.jpg", 9999)
            print("✅ 移除不存在组中文件处理正常")
        except Exception as e:
            print(f"❌ 移除不存在组中文件出错: {str(e)}")
        
        # 测试3：空结果集的移除操作
        print("\n测试3: 空结果集的移除操作")
        app.current_results = {}
        
        try:
            app.remove_single_file("/test/file1.jpg", 1024)
            print("✅ 空结果集移除处理正常")
        except Exception as e:
            print(f"❌ 空结果集移除出错: {str(e)}")
        
        root.destroy()
        
    except Exception as e:
        print(f"边界测试出错: {str(e)}")

if __name__ == "__main__":
    test_single_file_removal()
    test_batch_removal()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("🔧 移除修复测试完成")
    print("=" * 50)
    
    print("\n📊 修复内容:")
    print("✅ 修复了单个文件移除时组不被删除的问题")
    print("✅ 确保组被删除时强制使用完全更新")
    print("✅ 保持批量移除的正确行为")
    print("✅ 增强了边界情况的处理")
    
    print("\n🎯 预期效果:")
    print("- 移除文件后只剩1个文件的组会被立即删除")
    print("- 界面显示会立即反映数据变化")
    print("- 不会再出现只有1个文件的'重复'组")
    print("- 移除操作的响应更加可靠")
