#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除/移除文件后的刷新性能问题
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_refresh_performance():
    """测试刷新性能问题"""
    print("测试删除/移除文件后的刷新性能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="refresh_perf_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        # 组1：按大小搜索的文件（图片）
        size_files = []
        for i in range(5):
            file_path = os.path.join(test_dir, f"image_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * 1024)  # 1KB文件
            size_files.append(file_path)
            print(f"创建图片: {os.path.basename(file_path)}")
        
        # 组2：按时长搜索的文件（视频）
        duration_files = []
        for i in range(5):
            file_path = os.path.join(test_dir, f"video_{i}.mp4")
            with open(file_path, 'w') as f:
                f.write('V' * 2048)  # 2KB文件
            duration_files.append(file_path)
            print(f"创建视频: {os.path.basename(file_path)}")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 测试1：按大小搜索后的刷新性能
        print(f"\n=== 测试1: 按大小搜索后的刷新性能 ===")
        
        # 执行按大小搜索
        app._search_by_size_worker()
        print(f"按大小搜索完成，找到 {len(app.current_results)} 个组")
        
        # 测试删除文件后的刷新时间
        if app.current_results:
            first_size = next(iter(app.current_results.keys()))
            first_files = app.current_results[first_size]
            if first_files:
                file_to_delete = first_files[0]
                
                print(f"准备删除文件: {os.path.basename(file_to_delete)}")
                
                # 测量刷新时间
                start_time = time.time()
                app.delete_file(file_to_delete, first_size)
                refresh_time = time.time() - start_time
                
                print(f"按大小搜索 - 删除后刷新耗时: {refresh_time:.3f} 秒")
        
        # 测试2：模拟按时长搜索后的刷新性能
        print(f"\n=== 测试2: 模拟按时长搜索后的刷新性能 ===")
        
        # 手动创建时长搜索的结果（模拟）
        duration_results = {
            30: duration_files[:2],  # 30秒的视频
            60: duration_files[2:4], # 60秒的视频
            90: duration_files[4:]   # 90秒的视频
        }
        
        app.current_results = duration_results
        
        print(f"模拟时长搜索结果: {len(duration_results)} 个组")
        
        # 测试删除文件后的刷新时间
        file_to_delete = duration_files[0]
        print(f"准备删除文件: {os.path.basename(file_to_delete)}")
        
        # 测量刷新时间
        start_time = time.time()
        app.delete_file(file_to_delete, 30)
        refresh_time = time.time() - start_time
        
        print(f"按时长搜索 - 删除后刷新耗时: {refresh_time:.3f} 秒")
        
        # 测试3：直接测试display_results的性能
        print(f"\n=== 测试3: 直接测试display_results性能 ===")
        
        # 测试大小结果的显示性能
        size_results = {1024: size_files}
        
        start_time = time.time()
        app.display_results(size_results)
        size_display_time = time.time() - start_time
        print(f"显示大小结果耗时: {size_display_time:.3f} 秒")
        
        # 测试时长结果的显示性能
        start_time = time.time()
        app.display_results(duration_results)
        duration_display_time = time.time() - start_time
        print(f"显示时长结果耗时: {duration_display_time:.3f} 秒")
        
        # 性能对比
        print(f"\n=== 性能对比 ===")
        print(f"大小结果显示: {size_display_time:.3f} 秒")
        print(f"时长结果显示: {duration_display_time:.3f} 秒")
        
        if duration_display_time > size_display_time * 1.5:
            print("⚠ 时长结果显示明显慢于大小结果显示")
            print("可能的原因:")
            print("- 判断搜索类型时的额外处理")
            print("- 排序算法的差异")
            print("- 文件信息获取的差异")
        else:
            print("✓ 显示性能基本一致")
        
        # 测试4：分析具体的性能瓶颈
        print(f"\n=== 测试4: 分析性能瓶颈 ===")
        
        # 测试搜索类型判断的耗时
        start_time = time.time()
        first_key = next(iter(duration_results.keys()))
        first_files = next(iter(duration_results.values()))
        if first_files:
            first_file = first_files[0]
            file_ext = os.path.splitext(first_file)[1].lower()
            video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob', '.asf', '.rm', '.rmvb'}
            is_duration_search = file_ext in video_extensions and isinstance(first_key, (int, float)) and first_key < 100000
        type_check_time = time.time() - start_time
        print(f"搜索类型判断耗时: {type_check_time:.6f} 秒")
        
        # 测试排序的耗时
        start_time = time.time()
        if is_duration_search:
            sorted_results = sorted(duration_results.items(), key=lambda x: float(x[0]) if isinstance(x[0], (int, float, str)) and str(x[0]).replace('.', '').isdigit() else 0, reverse=True)
        else:
            sorted_results = sorted(duration_results.items(), key=lambda x: int(x[0]) if isinstance(x[0], (int, str)) and str(x[0]).isdigit() else 0, reverse=True)
        sort_time = time.time() - start_time
        print(f"排序耗时: {sort_time:.6f} 秒")
        
        # 测试文件信息获取的耗时
        start_time = time.time()
        for file in duration_files[:3]:  # 测试前3个文件
            display_info = app.get_file_display_info_cached(file)
        file_info_time = time.time() - start_time
        print(f"文件信息获取耗时 (3个文件): {file_info_time:.6f} 秒")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_incremental_update_performance():
    """测试增量更新的性能"""
    print("\n" + "=" * 50)
    print("测试增量更新的性能")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 创建大量测试数据
        large_results = {}
        for i in range(20):  # 20个组
            size = 1024 * (i + 1)
            files = [f"/test/file_{size}_{j}.jpg" for j in range(5)]  # 每组5个文件
            large_results[size] = files
        
        print(f"创建测试数据: {len(large_results)} 个组，{sum(len(files) for files in large_results.values())} 个文件")
        
        # 测试完全更新的性能
        start_time = time.time()
        app.display_results(large_results)
        full_update_time = time.time() - start_time
        print(f"完全更新耗时: {full_update_time:.3f} 秒")
        
        # 测试增量更新的性能（如果有的话）
        deleted_files = ["/test/file_1024_0.jpg"]
        
        start_time = time.time()
        # 模拟增量更新
        success = app.incremental_update_display(deleted_files)
        incremental_time = time.time() - start_time
        
        print(f"增量更新耗时: {incremental_time:.3f} 秒")
        print(f"增量更新成功: {success}")
        
        if success and incremental_time < full_update_time * 0.5:
            print("✓ 增量更新性能良好")
        else:
            print("⚠ 增量更新可能存在性能问题")
        
        root.destroy()
        
    except Exception as e:
        print(f"增量更新测试出错: {str(e)}")

if __name__ == "__main__":
    test_refresh_performance()
    test_incremental_update_performance()
    
    print("\n" + "=" * 50)
    print("🔍 刷新性能测试完成")
    print("=" * 50)
    
    print("\n📊 测试目的:")
    print("- 找出删除/移除文件后刷新变慢的具体原因")
    print("- 对比按大小和按时长搜索的刷新性能差异")
    print("- 分析display_results方法的性能瓶颈")
    print("- 验证增量更新的效果")
    
    print("\n💡 可能的原因:")
    print("1. 搜索类型判断时的额外处理")
    print("2. 时长排序算法比大小排序复杂")
    print("3. 文件信息获取时的差异")
    print("4. 增量更新机制的问题")
    print("5. 界面组件创建的性能差异")
