#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际测试排序后的预览功能
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_files():
    """创建测试文件"""
    test_dir = tempfile.mkdtemp(prefix="actual_sort_test_")
    print(f"创建测试目录: {test_dir}")
    
    # 创建不同大小的测试文件
    test_files = []
    sizes = [1024, 2048, 1024, 4096, 2048]  # 有重复大小
    
    for i, size in enumerate(sizes):
        file_path = os.path.join(test_dir, f"test_{i}.jpg")
        with open(file_path, 'w') as f:
            f.write('A' * size)
        test_files.append(file_path)
    
    print(f"创建了 {len(test_files)} 个测试文件")
    return test_dir, test_files

def test_actual_sorting_preview():
    """实际测试排序预览功能"""
    print("实际测试排序后的预览功能")
    print("=" * 50)
    
    test_dir, test_files = create_test_files()
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        # 创建可见的窗口进行测试
        root = tk.Tk()
        root.title("排序预览功能测试")
        root.geometry("800x600")
        
        app = FileSearchApp(root)
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"执行搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        # 显示原始结果
        print(f"显示原始结果...")
        app.display_results(app.current_results)
        root.update()
        
        print(f"原始结果已显示，请检查文件路径是否有蓝色下划线")
        
        # 等待用户确认
        input("按回车键继续测试排序功能...")
        
        # 执行按大小排序
        print(f"执行按大小排序...")
        app.sort_results_by_size(app.current_results)
        root.update()
        
        print(f"排序完成！")
        print(f"请检查排序后的文件路径是否仍有蓝色下划线")
        print(f"请尝试点击文件路径看是否能预览")
        
        # 检查标签配置
        tag_names = app.text_area.tag_names()
        file_tags = [tag for tag in tag_names if tag.startswith("file_")]
        print(f"排序后的文件标签数量: {len(file_tags)}")
        
        if file_tags:
            first_tag = file_tags[0]
            try:
                color = app.text_area.tag_cget(first_tag, "foreground")
                underline = app.text_area.tag_cget(first_tag, "underline")
                print(f"第一个标签样式: 颜色={color}, 下划线={underline}")
                
                # 检查标签范围
                ranges = app.text_area.tag_ranges(first_tag)
                if ranges:
                    tagged_text = app.text_area.get(ranges[0], ranges[1])
                    print(f"标签覆盖的文本: '{tagged_text}'")
                else:
                    print("标签没有范围")
                    
            except Exception as e:
                print(f"检查标签配置时出错: {str(e)}")
        
        # 等待用户测试
        input("请测试点击文件路径预览功能，然后按回车键结束...")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except:
            pass

def debug_tag_creation():
    """调试标签创建过程"""
    print("\n" + "=" * 50)
    print("调试标签创建过程")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 手动创建测试数据
        test_results = {
            1024: ["/test/file1.jpg", "/test/file2.jpg"],
            2048: ["/test/file3.jpg"]
        }
        
        app.current_results = test_results
        app.current_search_type = "size"
        
        print("显示原始结果...")
        app.display_results(test_results)
        
        # 检查原始标签
        original_tags = [tag for tag in app.text_area.tag_names() if tag.startswith("file_")]
        print(f"原始标签数量: {len(original_tags)}")
        
        for i, tag in enumerate(original_tags):
            try:
                color = app.text_area.tag_cget(tag, "foreground")
                underline = app.text_area.tag_cget(tag, "underline")
                ranges = app.text_area.tag_ranges(tag)
                if ranges:
                    text = app.text_area.get(ranges[0], ranges[1])
                    print(f"原始标签{i+1}: {tag}")
                    print(f"  样式: 颜色={color}, 下划线={underline}")
                    print(f"  文本: '{text}'")
            except Exception as e:
                print(f"检查原始标签{i+1}时出错: {str(e)}")
        
        print(f"\n执行排序...")
        app.sort_results_by_size(test_results)
        
        # 检查排序后的标签
        sorted_tags = [tag for tag in app.text_area.tag_names() if tag.startswith("file_")]
        print(f"排序后标签数量: {len(sorted_tags)}")
        
        for i, tag in enumerate(sorted_tags):
            try:
                color = app.text_area.tag_cget(tag, "foreground")
                underline = app.text_area.tag_cget(tag, "underline")
                ranges = app.text_area.tag_ranges(tag)
                if ranges:
                    text = app.text_area.get(ranges[0], ranges[1])
                    print(f"排序后标签{i+1}: {tag}")
                    print(f"  样式: 颜色={color}, 下划线={underline}")
                    print(f"  文本: '{text}'")
                else:
                    print(f"排序后标签{i+1}: {tag} - 没有范围")
            except Exception as e:
                print(f"检查排序后标签{i+1}时出错: {str(e)}")
        
        # 检查界面内容
        content = app.text_area.get("1.0", tk.END)
        print(f"\n界面内容预览（前500字符）:")
        print(content[:500])
        
        root.destroy()
        
    except Exception as e:
        print(f"调试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 先进行调试
    debug_tag_creation()
    
    # 询问是否进行实际测试
    response = input("\n是否进行实际的可视化测试？(y/n): ")
    if response.lower() == 'y':
        test_actual_sorting_preview()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    print("\n如果排序后文件路径没有蓝色下划线，可能的原因:")
    print("1. 标签配置没有正确应用")
    print("2. 标签范围定位有误")
    print("3. 文本区域的样式被重置")
    print("4. 标签名称冲突或重复")
    
    print("\n请检查:")
    print("- 排序后是否有文件标签")
    print("- 标签的颜色和下划线配置")
    print("- 标签覆盖的文本内容")
    print("- 是否有错误信息输出")
