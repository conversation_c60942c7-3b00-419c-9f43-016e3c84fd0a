#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试只剩一个文件的组是否会被自动删除
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_single_file_group_removal():
    """测试只剩一个文件的组是否会被自动删除"""
    print("测试只剩一个文件的组自动删除功能")
    print("=" * 60)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="single_group_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        # 组1：3个相同文件（1KB）
        content_1kb = 'A' * 1024
        group1_files = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"group1_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write(content_1kb)
            group1_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (1KB)")
        
        # 组2：2个相同文件（2KB）
        content_2kb = 'B' * 2048
        group2_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"group2_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write(content_2kb)
            group2_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (2KB)")
        
        # 导入并测试应用
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 设置目录并搜索
        app.current_directory = test_dir
        app.include_images_var.set(True)
        app._search_by_size_worker()
        
        print(f"\n初始搜索结果:")
        print(f"找到 {len(app.current_results)} 个重复文件组")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 测试1：删除组1中的2个文件，应该还剩1个文件，组应该被删除
        print(f"\n=== 测试1: 删除组1中的2个文件 ===")
        if 1024 in app.current_results:
            files_1kb = app.current_results[1024].copy()
            print(f"组1原有文件: {len(files_1kb)} 个")
            
            # 删除第一个文件
            print(f"删除第1个文件: {os.path.basename(files_1kb[0])}")
            app.delete_file(files_1kb[0], 1024)
            
            print(f"删除后组数: {len(app.current_results)}")
            if 1024 in app.current_results:
                print(f"  大小1024组还有: {len(app.current_results[1024])} 个文件")
            else:
                print(f"  大小1024组已被删除")
            
            # 删除第二个文件
            if 1024 in app.current_results and len(app.current_results[1024]) > 0:
                print(f"删除第2个文件: {os.path.basename(files_1kb[1])}")
                app.delete_file(files_1kb[1], 1024)
                
                print(f"删除后组数: {len(app.current_results)}")
                if 1024 in app.current_results:
                    print(f"  大小1024组还有: {len(app.current_results[1024])} 个文件")
                    print("  ✗ 组1未被自动删除（只剩1个文件时应该删除）")
                else:
                    print(f"  ✓ 大小1024组已被正确删除")
        
        # 测试2：删除组2中的1个文件，应该还剩1个文件，组应该被删除
        print(f"\n=== 测试2: 删除组2中的1个文件 ===")
        if 2048 in app.current_results:
            files_2kb = app.current_results[2048].copy()
            print(f"组2原有文件: {len(files_2kb)} 个")
            
            # 删除一个文件
            print(f"删除文件: {os.path.basename(files_2kb[0])}")
            app.delete_file(files_2kb[0], 2048)
            
            print(f"删除后组数: {len(app.current_results)}")
            if 2048 in app.current_results:
                print(f"  大小2048组还有: {len(app.current_results[2048])} 个文件")
                print("  ✗ 组2未被自动删除（只剩1个文件时应该删除）")
            else:
                print(f"  ✓ 大小2048组已被正确删除")
        
        # 测试3：手动验证逻辑
        print(f"\n=== 测试3: 手动验证删除逻辑 ===")
        
        # 创建一个新的测试组
        test_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"test_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('C' * 512)
            test_files.append(file_path)
        
        # 手动设置结果
        app.current_results[512] = test_files
        print(f"手动创建测试组: 大小512, {len(test_files)}个文件")
        
        # 删除一个文件并检查逻辑
        file_to_delete = test_files[0]
        size_key = 512
        
        print(f"删除前: current_results[512] = {app.current_results[512]}")
        
        # 模拟删除逻辑
        if size_key in app.current_results and file_to_delete in app.current_results[size_key]:
            app.current_results[size_key].remove(file_to_delete)
            print(f"移除文件后: current_results[512] = {app.current_results[512]}")
            print(f"剩余文件数: {len(app.current_results[512])}")
            
            if len(app.current_results[512]) < 2:
                del app.current_results[512]
                print(f"✓ 组被正确删除（剩余文件数 < 2）")
            else:
                print(f"✗ 组未被删除（剩余文件数 >= 2）")
        
        print(f"\n最终结果:")
        print(f"剩余组数: {len(app.current_results)}")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_batch_delete_group_removal():
    """测试批量删除时的组自动删除"""
    print("\n" + "=" * 60)
    print("测试批量删除时的组自动删除功能")
    print("=" * 60)
    
    test_dir = tempfile.mkdtemp(prefix="batch_group_test_")
    
    try:
        # 创建测试文件
        # 组1：3个文件
        files_3kb = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"batch_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('D' * 3072)
            files_3kb.append(file_path)
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 手动设置结果
        app.current_results = {3072: files_3kb}
        
        print(f"初始状态: 大小3072组有 {len(files_3kb)} 个文件")
        
        # 模拟勾选2个文件进行批量删除
        for file_path in files_3kb[:2]:
            if file_path not in app.checkbox_vars:
                app.checkbox_vars[file_path] = tk.BooleanVar()
            app.checkbox_vars[file_path].set(True)
        
        print(f"勾选了 2 个文件进行批量删除")
        
        # 执行批量删除
        app.delete_selected_files()
        
        print(f"批量删除后:")
        if 3072 in app.current_results:
            print(f"  大小3072组还有: {len(app.current_results[3072])} 个文件")
            print("  ✗ 组未被自动删除（只剩1个文件时应该删除）")
        else:
            print(f"  ✓ 大小3072组已被正确删除")
        
        root.destroy()
        
    except Exception as e:
        print(f"批量删除测试出错: {str(e)}")
    
    finally:
        try:
            shutil.rmtree(test_dir)
        except:
            pass

def debug_delete_logic():
    """调试删除逻辑"""
    print("\n" + "=" * 60)
    print("调试删除逻辑")
    print("=" * 60)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 创建测试数据
        test_results = {
            1024: ["/test/file1.jpg", "/test/file2.jpg"],
            2048: ["/test/file3.jpg", "/test/file4.jpg", "/test/file5.jpg"]
        }
        
        app.current_results = test_results.copy()
        
        print("初始状态:")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 模拟删除操作
        print("\n模拟删除 /test/file1.jpg (大小1024):")
        
        size_key = 1024
        file_to_delete = "/test/file1.jpg"
        
        if size_key in app.current_results and file_to_delete in app.current_results[size_key]:
            print(f"删除前: {app.current_results[size_key]}")
            app.current_results[size_key].remove(file_to_delete)
            print(f"删除后: {app.current_results[size_key]}")
            print(f"剩余文件数: {len(app.current_results[size_key])}")
            
            if len(app.current_results[size_key]) < 2:
                print("条件满足: len(files) < 2，应该删除组")
                del app.current_results[size_key]
                print("✓ 组已删除")
            else:
                print("条件不满足: len(files) >= 2，保留组")
        
        print("\n最终状态:")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        root.destroy()
        
    except Exception as e:
        print(f"调试出错: {str(e)}")

if __name__ == "__main__":
    test_single_file_group_removal()
    test_batch_delete_group_removal()
    debug_delete_logic()
    
    print("\n" + "=" * 60)
    print("🔍 组自动删除测试完成")
    print("=" * 60)
    
    print("\n📋 测试总结:")
    print("1. 测试了单个文件删除后的组自动删除")
    print("2. 测试了批量删除后的组自动删除")
    print("3. 调试了删除逻辑的执行过程")
    
    print("\n💡 预期行为:")
    print("- 当一个重复文件组只剩下1个文件时，该组应该自动从结果中删除")
    print("- 因为只有1个文件的组不再是'重复文件组'")
    print("- 这适用于所有删除操作：单个删除、批量删除、移出列表")
