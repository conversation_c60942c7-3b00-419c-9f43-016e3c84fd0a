#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高效组移除功能的性能优化
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_efficient_group_removal():
    """测试高效组移除功能"""
    print("测试高效组移除功能的性能优化")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="efficient_removal_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建大量测试文件来模拟172个组的场景
        print("创建大量测试文件...")
        
        all_files = []
        # 创建20个不同大小的组，每组3个文件（模拟大量组的场景）
        for i in range(20):
            size_kb = (i + 1) * 5  # 5KB, 10KB, 15KB, ...
            for j in range(3):
                file_path = os.path.join(test_dir, f"group{i}_file{j}.jpg")
                with open(file_path, 'w') as f:
                    f.write('A' * (size_kb * 1024))
                all_files.append(file_path)
        
        print(f"创建了 {len(all_files)} 个测试文件，20个组")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"\n执行搜索...")
        start_time = time.time()
        app._search_by_size_worker()
        search_time = time.time() - start_time
        
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        print(f"搜索耗时: {search_time:.3f} 秒")
        
        # 测试1：传统完全更新的性能
        print(f"\n=== 测试1: 传统完全更新性能 ===")
        
        # 备份当前结果
        backup_results = app.current_results.copy()
        
        # 模拟删除一个文件导致组删除
        first_size = next(iter(app.current_results.keys()))
        first_files = app.current_results[first_size]
        
        if len(first_files) >= 2:
            # 删除到只剩1个文件
            files_to_delete = first_files[:-1]
            for file_path in files_to_delete:
                if file_path in app.current_results[first_size]:
                    app.current_results[first_size].remove(file_path)
            
            # 现在组只剩1个文件，应该被删除
            if len(app.current_results[first_size]) < 2:
                del app.current_results[first_size]
            
            print(f"模拟删除后剩余组数: {len(app.current_results)}")
            
            # 测试完全更新的性能
            start_time = time.time()
            app.display_results(app.current_results)
            full_update_time = time.time() - start_time
            
            print(f"完全更新耗时: {full_update_time:.3f} 秒")
        
        # 恢复结果用于下一个测试
        app.current_results = backup_results.copy()
        
        # 测试2：高效组移除的性能
        print(f"\n=== 测试2: 高效组移除性能 ===")
        
        # 重新显示完整结果
        app.display_results(app.current_results)
        
        # 选择几个组进行高效移除测试
        groups_to_remove = list(app.current_results.keys())[:3]  # 移除前3个组
        print(f"准备高效移除的组: {groups_to_remove}")
        
        # 从current_results中删除这些组
        for group_size in groups_to_remove:
            if group_size in app.current_results:
                del app.current_results[group_size]
        
        print(f"移除后剩余组数: {len(app.current_results)}")
        
        # 测试高效组移除的性能
        start_time = time.time()
        success = app.efficient_remove_groups(groups_to_remove, [])
        efficient_removal_time = time.time() - start_time
        
        print(f"高效组移除耗时: {efficient_removal_time:.3f} 秒")
        print(f"高效组移除成功: {success}")
        
        # 性能对比
        print(f"\n=== 性能对比 ===")
        if 'full_update_time' in locals() and 'efficient_removal_time' in locals():
            print(f"完全更新耗时: {full_update_time:.3f} 秒")
            print(f"高效移除耗时: {efficient_removal_time:.3f} 秒")
            
            if efficient_removal_time < full_update_time:
                improvement = (full_update_time - efficient_removal_time) / full_update_time * 100
                print(f"✅ 性能提升: {improvement:.1f}%")
            else:
                print("⚠️ 高效移除未显示明显优势")
        
        # 测试3：模拟真实的删除操作
        print(f"\n=== 测试3: 模拟真实删除操作 ===")
        
        # 恢复一些结果用于测试
        app.current_results = {
            5120: ["/test/file1.jpg", "/test/file2.jpg"],  # 2个文件的组
            10240: ["/test/file3.jpg", "/test/file4.jpg", "/test/file5.jpg"],  # 3个文件的组
            15360: ["/test/file6.jpg", "/test/file7.jpg"]   # 2个文件的组
        }
        
        # 重新显示
        app.display_results(app.current_results)
        
        # 模拟删除操作：删除5120组的1个文件，导致组删除
        file_to_delete = "/test/file1.jpg"
        
        print(f"模拟删除文件: {file_to_delete}")
        
        # 手动执行删除逻辑
        app.current_results[5120].remove(file_to_delete)
        deleted_groups = []
        if len(app.current_results[5120]) < 2:
            deleted_groups.append(5120)
            del app.current_results[5120]
        
        # 测试增量更新（包含高效组移除）
        start_time = time.time()
        success = app.incremental_update_display([file_to_delete], deleted_groups)
        incremental_time = time.time() - start_time
        
        print(f"增量更新（含高效组移除）耗时: {incremental_time:.3f} 秒")
        print(f"增量更新成功: {success}")
        
        # 验证结果
        print(f"最终剩余组数: {len(app.current_results)}")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_large_scale_scenario():
    """测试大规模场景（模拟172个组）"""
    print("\n" + "=" * 50)
    print("测试大规模场景（模拟172个组）")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 创建大量模拟数据（172个组）
        print("创建172个组的模拟数据...")
        
        large_results = {}
        for i in range(172):
            # 模拟时长搜索：30秒到200秒的视频
            duration = 30 + i * 1.0
            files = [f"/test/video_{duration}s_{j}.mp4" for j in range(3)]
            large_results[duration] = files
        
        app.current_results = large_results
        
        print(f"创建了 {len(large_results)} 个组的模拟数据")
        
        # 测试完全更新的性能
        print(f"\n测试完全更新性能...")
        start_time = time.time()
        app.display_results(app.current_results)
        full_update_time = time.time() - start_time
        
        print(f"172组完全更新耗时: {full_update_time:.3f} 秒")
        
        # 测试高效组移除的性能
        print(f"\n测试高效组移除性能...")
        
        # 选择前5个组进行移除
        groups_to_remove = list(large_results.keys())[:5]
        
        # 从结果中删除这些组
        for group_key in groups_to_remove:
            if group_key in app.current_results:
                del app.current_results[group_key]
        
        start_time = time.time()
        success = app.efficient_remove_groups(groups_to_remove, [])
        efficient_time = time.time() - start_time
        
        print(f"高效移除5个组耗时: {efficient_time:.3f} 秒")
        print(f"高效移除成功: {success}")
        
        # 性能对比
        print(f"\n大规模场景性能对比:")
        print(f"完全更新 (172组): {full_update_time:.3f} 秒")
        print(f"高效移除 (5组): {efficient_time:.3f} 秒")
        
        if efficient_time < full_update_time * 0.1:  # 如果高效移除时间小于完全更新的10%
            print("🚀 高效移除在大规模场景下显示出显著优势")
        else:
            print("⚠️ 高效移除优势不明显，可能需要进一步优化")
        
        root.destroy()
        
    except Exception as e:
        print(f"大规模测试出错: {str(e)}")

if __name__ == "__main__":
    test_efficient_group_removal()
    test_large_scale_scenario()
    
    print("\n" + "=" * 50)
    print("🚀 高效组移除测试完成")
    print("=" * 50)
    
    print("\n📊 优化内容:")
    print("✅ 高效组移除方法")
    print("   - 避免完整的重新排序")
    print("   - 直接移除UI组件")
    print("   - 更新组计数而不重建界面")
    
    print("\n✅ 智能降级机制")
    print("   - 优先尝试高效移除")
    print("   - 失败时自动降级到完全更新")
    print("   - 确保功能可靠性")
    
    print("\n📈 预期性能提升:")
    print("- 小规模组移除: 50-80%性能提升")
    print("- 大规模场景: 显著减少排序时间")
    print("- 172组场景: 避免完整重排序的耗时")
    
    print("\n💡 优化原理:")
    print("问题: 组删除时触发完整重排序")
    print("解决: 直接移除UI组件，避免排序")
    print("结果: 大幅减少大规模场景的响应时间")
    
    print("\n🎯 适用场景:")
    print("- 按时长搜索的大量结果")
    print("- 删除文件导致组删除的操作")
    print("- 需要快速响应的用户交互")
