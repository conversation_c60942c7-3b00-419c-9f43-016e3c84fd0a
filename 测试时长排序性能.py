#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按时长排序的性能问题
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_duration_sorting_performance():
    """测试时长排序性能"""
    print("测试按时长排序的性能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="duration_perf_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建多个不同时长的视频文件组
        durations = [30.5, 120.0, 60.2, 180.5, 90.1, 45.3, 150.8, 75.6, 200.2, 35.9]
        all_files = []
        
        for duration in durations:
            # 每个时长创建3个文件
            for i in range(3):
                file_path = os.path.join(test_dir, f"video_{duration}s_{i:02d}.mp4")
                # 创建不同大小的文件来模拟不同的视频
                content = 'V' * int(duration * 100)
                with open(file_path, 'w') as f:
                    f.write(content)
                all_files.append(file_path)
        
        print(f"创建了 {len(all_files)} 个测试视频文件")
        
        # 导入并测试应用
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 设置目录
        app.current_directory = test_dir
        
        # 测试1：按时长搜索的性能
        print(f"\n=== 测试1: 按时长搜索性能 ===")
        
        start_time = time.time()
        app._search_by_duration_worker()
        search_time = time.time() - start_time
        
        print(f"按时长搜索耗时: {search_time:.3f} 秒")
        print(f"找到 {len(app.current_results)} 个重复时长组")
        
        # 测试2：显示结果的性能（包含排序）
        print(f"\n=== 测试2: 显示结果性能（包含排序） ===")
        
        # 保存搜索结果
        search_results = app.current_results.copy()
        
        # 测试多次显示结果的性能
        display_times = []
        for i in range(5):
            start_time = time.time()
            app.display_results(search_results)
            display_time = time.time() - start_time
            display_times.append(display_time)
            print(f"第 {i+1} 次显示耗时: {display_time:.3f} 秒")
        
        avg_display_time = sum(display_times) / len(display_times)
        print(f"平均显示耗时: {avg_display_time:.3f} 秒")
        
        # 测试3：对比按大小搜索的性能
        print(f"\n=== 测试3: 对比按大小搜索性能 ===")
        
        start_time = time.time()
        app._search_by_size_worker()
        size_search_time = time.time() - start_time
        
        print(f"按大小搜索耗时: {size_search_time:.3f} 秒")
        print(f"找到 {len(app.current_results)} 个重复大小组")
        
        # 显示大小搜索结果
        start_time = time.time()
        app.display_results(app.current_results)
        size_display_time = time.time() - start_time
        
        print(f"显示大小结果耗时: {size_display_time:.3f} 秒")
        
        # 性能对比
        print(f"\n=== 性能对比 ===")
        print(f"时长搜索 vs 大小搜索: {search_time:.3f}s vs {size_search_time:.3f}s")
        print(f"时长显示 vs 大小显示: {avg_display_time:.3f}s vs {size_display_time:.3f}s")
        
        if search_time > size_search_time * 1.5:
            print("⚠ 时长搜索明显慢于大小搜索")
        
        if avg_display_time > size_display_time * 1.5:
            print("⚠ 时长显示明显慢于大小显示")
        
        # 测试4：缓存效果验证
        print(f"\n=== 测试4: 缓存效果验证 ===")
        
        print(f"文件信息缓存数量: {len(app._file_info_cache)}")
        print(f"显示信息缓存数量: {len(app._display_info_cache)}")
        
        # 检查时长缓存
        duration_cached_count = 0
        for path, info in app._file_info_cache.items():
            if 'duration' in info:
                duration_cached_count += 1
        
        print(f"已缓存时长的文件数: {duration_cached_count}")
        
        if duration_cached_count < len(all_files) * 0.8:
            print("⚠ 时长缓存覆盖率较低，可能存在重复获取")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_sorting_algorithm_performance():
    """测试排序算法性能"""
    print("\n" + "=" * 50)
    print("测试排序算法性能")
    print("=" * 50)
    
    try:
        # 创建大量测试数据
        print("创建测试数据...")
        
        # 时长数据（模拟真实场景）
        duration_data = {}
        for i in range(100):  # 100个不同时长
            duration = 30.0 + i * 2.5  # 30秒到280秒
            files = [f"/test/video_{duration}s_{j}.mp4" for j in range(5)]  # 每个时长5个文件
            duration_data[duration] = files
        
        # 大小数据
        size_data = {}
        for i in range(100):  # 100个不同大小
            size = 1024 * (i + 1)  # 1KB到100KB
            files = [f"/test/file_{size}b_{j}.txt" for j in range(5)]
            size_data[size] = files
        
        print(f"创建了时长数据: {len(duration_data)} 组")
        print(f"创建了大小数据: {len(size_data)} 组")
        
        # 测试时长排序性能
        print(f"\n测试时长排序性能...")
        start_time = time.time()
        sorted_duration = sorted(duration_data.items(), 
                               key=lambda x: float(x[0]) if isinstance(x[0], (int, float, str)) and str(x[0]).replace('.', '').isdigit() else 0, 
                               reverse=True)
        duration_sort_time = time.time() - start_time
        print(f"时长排序耗时: {duration_sort_time:.6f} 秒")
        
        # 测试大小排序性能
        print(f"\n测试大小排序性能...")
        start_time = time.time()
        sorted_size = sorted(size_data.items(), 
                           key=lambda x: int(x[0]) if isinstance(x[0], (int, str)) and str(x[0]).isdigit() else 0, 
                           reverse=True)
        size_sort_time = time.time() - start_time
        print(f"大小排序耗时: {size_sort_time:.6f} 秒")
        
        # 性能对比
        print(f"\n排序性能对比:")
        print(f"时长排序: {duration_sort_time:.6f} 秒")
        print(f"大小排序: {size_sort_time:.6f} 秒")
        
        if duration_sort_time > size_sort_time * 2:
            print("⚠ 时长排序算法可能存在性能问题")
        else:
            print("✓ 排序算法性能正常")
        
    except Exception as e:
        print(f"排序性能测试出错: {str(e)}")

def analyze_potential_issues():
    """分析潜在的性能问题"""
    print("\n" + "=" * 50)
    print("分析潜在的性能问题")
    print("=" * 50)
    
    print("可能的性能瓶颈:")
    print("1. 视频时长获取 - 需要解码视频文件头")
    print("2. 文件I/O操作 - 大量文件的读取")
    print("3. 界面更新 - 创建大量UI组件")
    print("4. 缓存失效 - 重复获取相同信息")
    print("5. 排序算法 - 复杂的key函数")
    
    print("\n优化建议:")
    print("✓ 已实现: 视频时长缓存机制")
    print("✓ 已实现: 文件信息缓存机制")
    print("✓ 已实现: 显示信息缓存机制")
    print("? 待验证: 是否存在重复获取时长的情况")
    print("? 待验证: 排序算法是否可以优化")
    
    print("\n调试建议:")
    print("1. 在get_video_duration方法中添加调试信息")
    print("2. 监控缓存命中率")
    print("3. 分析界面更新的耗时")
    print("4. 检查是否有不必要的重复计算")

if __name__ == "__main__":
    test_duration_sorting_performance()
    test_sorting_algorithm_performance()
    analyze_potential_issues()
    
    print("\n" + "=" * 50)
    print("🔍 性能测试完成")
    print("=" * 50)
    
    print("\n📊 测试目的:")
    print("- 找出按时长排序慢的具体原因")
    print("- 验证是否在重新读取时长数据")
    print("- 对比不同操作的性能差异")
    print("- 提供针对性的优化建议")
