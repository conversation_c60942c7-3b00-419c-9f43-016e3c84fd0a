#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试删除和移除文件后组不自动删除的问题
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_delete_issue():
    """调试删除文件的问题"""
    print("调试删除文件后组不自动删除的问题")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="debug_delete_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建简单的测试文件
        # 只创建一个组：2个文件
        test_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"test_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * 1024)
            test_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)}")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"\n执行搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 测试删除操作
        print(f"\n=== 测试删除操作 ===")
        
        if 1024 in app.current_results and len(app.current_results[1024]) == 2:
            file_to_delete = test_files[0]
            print(f"准备删除文件: {os.path.basename(file_to_delete)}")
            print(f"删除前current_results: {app.current_results}")
            
            # 手动跟踪删除过程
            print(f"\n开始删除过程...")
            
            # 模拟delete_file的关键步骤
            size = 1024
            print(f"1. 从current_results[{size}]中移除文件")
            app.current_results[size].remove(file_to_delete)
            print(f"   移除后: {app.current_results[size]}")
            
            print(f"2. 检查组是否需要删除")
            if len(app.current_results[size]) < 2:
                print(f"   组只剩 {len(app.current_results[size])} 个文件，需要删除组")
                deleted_groups = [size]
                del app.current_results[size]
                print(f"   删除组后current_results: {app.current_results}")
            else:
                deleted_groups = []
                print(f"   组还有 {len(app.current_results[size])} 个文件，保留组")
            
            print(f"3. 调用缓存恢复")
            print(f"   deleted_files: [{os.path.basename(file_to_delete)}]")
            print(f"   deleted_groups: {deleted_groups}")
            
            # 手动调用缓存恢复
            success = app.load_from_cache_after_delete([file_to_delete], deleted_groups)
            print(f"   缓存恢复结果: {success}")
            
            print(f"\n删除后最终状态:")
            print(f"current_results: {app.current_results}")
            print(f"组数: {len(app.current_results)}")
            
            # 检查界面显示
            content = app.text_area.get("1.0", tk.END)
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            group_lines = [line for line in lines if "文件大小:" in line]
            print(f"界面显示的组数: {len(group_lines)}")
            
            if len(app.current_results) == 0 and len(group_lines) == 0:
                print("✅ 删除操作正确：组已被删除，界面已更新")
            elif len(app.current_results) == 0 and len(group_lines) > 0:
                print("❌ 数据正确但界面未更新")
            elif len(app.current_results) > 0:
                print("❌ 组未被正确删除")
                for size, files in app.current_results.items():
                    if len(files) == 1:
                        print(f"    发现单文件组: 大小{size}, 文件{files}")
        
        root.destroy()
        
    except Exception as e:
        print(f"调试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            shutil.rmtree(test_dir)
        except:
            pass

def debug_remove_issue():
    """调试移除文件的问题"""
    print("\n" + "=" * 50)
    print("调试移除文件后组不自动删除的问题")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="debug_remove_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        test_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"remove_test_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('B' * 2048)
            test_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)}")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"\n执行搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        # 测试移除操作
        print(f"\n=== 测试移除操作 ===")
        
        if 2048 in app.current_results and len(app.current_results[2048]) == 2:
            file_to_remove = test_files[0]
            print(f"准备移除文件: {os.path.basename(file_to_remove)}")
            print(f"移除前current_results: {app.current_results}")
            
            # 手动跟踪移除过程
            print(f"\n开始移除过程...")
            
            # 模拟remove_single_file的关键步骤
            size = 2048
            print(f"1. 从current_results[{size}]中移除文件")
            if file_to_remove in app.current_results[size]:
                app.current_results[size].remove(file_to_remove)
                print(f"   移除后: {app.current_results[size]}")
                
                print(f"2. 检查组是否需要删除")
                if len(app.current_results[size]) < 2:
                    print(f"   组只剩 {len(app.current_results[size])} 个文件，需要删除组")
                    del app.current_results[size]
                    print(f"   删除组后current_results: {app.current_results}")
                else:
                    print(f"   组还有 {len(app.current_results[size])} 个文件，保留组")
                
                print(f"3. 调用界面更新")
                # 直接调用display_results
                app.display_results(app.current_results)
                
                print(f"\n移除后最终状态:")
                print(f"current_results: {app.current_results}")
                print(f"组数: {len(app.current_results)}")
                
                # 检查界面显示
                content = app.text_area.get("1.0", tk.END)
                lines = [line.strip() for line in content.split('\n') if line.strip()]
                group_lines = [line for line in lines if "文件大小:" in line]
                print(f"界面显示的组数: {len(group_lines)}")
                
                if len(app.current_results) == 0 and len(group_lines) == 0:
                    print("✅ 移除操作正确：组已被删除，界面已更新")
                elif len(app.current_results) == 0 and len(group_lines) > 0:
                    print("❌ 数据正确但界面未更新")
                elif len(app.current_results) > 0:
                    print("❌ 组未被正确删除")
                    for size, files in app.current_results.items():
                        if len(files) == 1:
                            print(f"    发现单文件组: 大小{size}, 文件{files}")
        
        root.destroy()
        
    except Exception as e:
        print(f"调试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            shutil.rmtree(test_dir)
        except:
            pass

def check_cleanup_methods():
    """检查清理方法是否正常工作"""
    print("\n" + "=" * 50)
    print("检查清理方法")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试cleanup_single_file_groups方法
        print("测试cleanup_single_file_groups方法")
        
        test_results = {
            1024: ["/test/file1.jpg"],  # 单文件组，应该被清理
            2048: ["/test/file2.jpg", "/test/file3.jpg"],  # 正常组
            4096: ["/test/file4.jpg"],  # 另一个单文件组，应该被清理
        }
        
        print(f"清理前: {test_results}")
        app.cleanup_single_file_groups(test_results)
        print(f"清理后: {test_results}")
        
        single_groups = [size for size, files in test_results.items() if len(files) == 1]
        if not single_groups:
            print("✅ cleanup_single_file_groups工作正常")
        else:
            print(f"❌ cleanup_single_file_groups未清理单文件组: {single_groups}")
        
        # 测试display_results是否调用了清理方法
        print(f"\n测试display_results是否调用清理方法")
        
        test_results_with_single = {
            1024: ["/test/single.jpg"],  # 单文件组
            2048: ["/test/file1.jpg", "/test/file2.jpg"],  # 正常组
        }
        
        print(f"显示前: {test_results_with_single}")
        app.display_results(test_results_with_single)
        print(f"显示后: {test_results_with_single}")
        
        single_groups = [size for size, files in test_results_with_single.items() if len(files) == 1]
        if not single_groups:
            print("✅ display_results正确调用了清理方法")
        else:
            print(f"❌ display_results未清理单文件组: {single_groups}")
        
        root.destroy()
        
    except Exception as e:
        print(f"清理方法检查出错: {str(e)}")

if __name__ == "__main__":
    debug_delete_issue()
    debug_remove_issue()
    check_cleanup_methods()
    
    print("\n" + "=" * 50)
    print("🔍 调试完成")
    print("=" * 50)
    
    print("\n📊 调试目的:")
    print("- 找出删除和移除文件后组不自动删除的具体原因")
    print("- 验证修复代码是否正确执行")
    print("- 检查清理方法是否正常工作")
    print("- 确定问题出现在哪个环节")
    
    print("\n💡 可能的问题:")
    print("1. 修复代码没有正确执行")
    print("2. 清理方法存在bug")
    print("3. 界面更新时机问题")
    print("4. 缓存恢复逻辑问题")
    print("5. 增量更新vs完全更新的选择问题")
