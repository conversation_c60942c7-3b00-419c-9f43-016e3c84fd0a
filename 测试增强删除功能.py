#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的文件删除功能
"""

import os
import sys
import tempfile
import shutil
import stat

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_delete_functionality():
    """测试增强的删除功能"""
    print("测试增强的文件删除功能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="delete_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试1：正常文件删除
        print(f"\n=== 测试1: 正常文件删除 ===")
        
        normal_file = os.path.join(test_dir, "normal_file.txt")
        with open(normal_file, 'w') as f:
            f.write("这是一个正常文件")
        
        print(f"创建正常文件: {os.path.basename(normal_file)}")
        
        success, error_msg = app.enhanced_delete_file(normal_file)
        
        if success:
            print("✅ 正常文件删除成功")
        else:
            print(f"❌ 正常文件删除失败: {error_msg}")
        
        # 测试2：只读文件删除
        print(f"\n=== 测试2: 只读文件删除 ===")
        
        readonly_file = os.path.join(test_dir, "readonly_file.txt")
        with open(readonly_file, 'w') as f:
            f.write("这是一个只读文件")
        
        # 设置为只读
        os.chmod(readonly_file, stat.S_IREAD)
        print(f"创建只读文件: {os.path.basename(readonly_file)}")
        
        success, error_msg = app.enhanced_delete_file(readonly_file)
        
        if success:
            print("✅ 只读文件删除成功")
        else:
            print(f"❌ 只读文件删除失败: {error_msg}")
        
        # 测试3：不存在的文件
        print(f"\n=== 测试3: 不存在的文件 ===")
        
        nonexistent_file = os.path.join(test_dir, "nonexistent_file.txt")
        print(f"测试不存在的文件: {os.path.basename(nonexistent_file)}")
        
        success, error_msg = app.enhanced_delete_file(nonexistent_file)
        
        if not success and "不存在" in str(error_msg):
            print("✅ 正确识别文件不存在")
        else:
            print(f"❌ 未正确处理不存在的文件: success={success}, error={error_msg}")
        
        # 测试4：详细错误信息功能
        print(f"\n=== 测试4: 详细错误信息功能 ===")
        
        # 创建一个文件用于测试错误信息
        error_test_file = os.path.join(test_dir, "error_test_file.txt")
        with open(error_test_file, 'w') as f:
            f.write("用于测试错误信息的文件")
        
        # 设置为只读来模拟权限问题
        os.chmod(error_test_file, stat.S_IREAD)
        
        # 获取详细错误信息
        error_msg = app.get_detailed_error_message(error_test_file)
        print(f"详细错误信息:")
        print(error_msg)
        
        if "只读" in error_msg and "建议" in error_msg:
            print("✅ 详细错误信息功能正常")
        else:
            print("❌ 详细错误信息功能异常")
        
        # 清理测试文件
        try:
            os.chmod(error_test_file, stat.S_IWRITE)
            os.remove(error_test_file)
        except:
            pass
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试目录
        try:
            # 确保所有文件都可写，然后删除
            for root, dirs, files in os.walk(test_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        os.chmod(file_path, stat.S_IWRITE)
                    except:
                        pass
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def demonstrate_error_scenarios():
    """演示各种错误场景和解决方案"""
    print("\n" + "=" * 50)
    print("常见删除错误场景和解决方案")
    print("=" * 50)
    
    print("🔍 常见的文件删除错误:")
    
    print("\n1. ❌ [WinError 5] 拒绝访问")
    print("   原因:")
    print("   • 文件正在被其他程序使用")
    print("   • 文件具有只读属性")
    print("   • 没有足够的权限")
    print("   • 文件在系统保护的位置")
    
    print("\n   解决方案:")
    print("   ✅ 关闭正在使用文件的程序")
    print("   ✅ 移除文件的只读属性")
    print("   ✅ 以管理员身份运行程序")
    print("   ✅ 使用强制删除命令")
    
    print("\n2. ❌ [WinError 32] 另一个程序正在使用此文件")
    print("   原因:")
    print("   • 视频播放器正在播放该文件")
    print("   • 文件资源管理器正在预览该文件")
    print("   • 杀毒软件正在扫描该文件")
    print("   • 系统索引服务正在处理该文件")
    
    print("\n   解决方案:")
    print("   ✅ 关闭所有可能使用该文件的程序")
    print("   ✅ 结束相关的系统进程")
    print("   ✅ 重启计算机后再删除")
    print("   ✅ 使用文件解锁工具")
    
    print("\n3. ❌ [WinError 206] 文件名或扩展名太长")
    print("   原因:")
    print("   • Windows路径长度限制（260字符）")
    print("   • 深层嵌套的文件夹结构")
    
    print("\n   解决方案:")
    print("   ✅ 将文件移动到较短的路径")
    print("   ✅ 重命名父文件夹为更短的名称")
    print("   ✅ 使用支持长路径的工具")
    
    print("\n🛠️ 增强删除功能的优势:")
    print("✅ 多种删除方法自动尝试")
    print("✅ 智能识别错误原因")
    print("✅ 提供具体的解决建议")
    print("✅ 自动处理只读属性")
    print("✅ 支持Windows强制删除")
    print("✅ 详细的错误报告")

def analyze_specific_error():
    """分析您遇到的具体错误"""
    print("\n" + "=" * 50)
    print("分析您遇到的具体错误")
    print("=" * 50)
    
    error_file = r"V:/wangluo/女优 福利姬/完具酱 娜美妖姬\20.02.01巫女 25P+2V1.27G\V (1).mp4"
    
    print("🔍 错误分析:")
    print(f"文件路径: {error_file}")
    print(f"错误类型: [WinError 5] 拒绝访问")
    
    print("\n📊 可能的原因:")
    print("1. 🎥 视频文件正在被播放器使用")
    print("   - 可能有隐藏的播放器进程")
    print("   - 系统可能正在生成缩略图")
    print("   - 媒体索引服务可能正在处理")
    
    print("\n2. 🔒 文件权限问题")
    print("   - 网络驱动器(V:)的权限限制")
    print("   - 文件可能被标记为只读")
    print("   - 用户权限不足")
    
    print("\n3. 🌐 网络驱动器特殊性")
    print("   - 网络连接不稳定")
    print("   - 远程服务器权限设置")
    print("   - 网络驱动器映射问题")
    
    print("\n💡 建议的解决步骤:")
    print("1. ✅ 关闭所有视频播放器和媒体程序")
    print("2. ✅ 检查任务管理器中的相关进程")
    print("3. ✅ 右键文件 → 属性 → 取消只读")
    print("4. ✅ 以管理员身份运行程序")
    print("5. ✅ 检查网络驱动器连接状态")
    print("6. ✅ 尝试先复制文件到本地再删除")
    
    print("\n🚀 增强删除功能会自动:")
    print("• 尝试移除只读属性")
    print("• 使用重命名删除法")
    print("• 调用Windows强制删除命令")
    print("• 提供详细的错误诊断")
    print("• 给出具体的解决建议")

if __name__ == "__main__":
    test_enhanced_delete_functionality()
    demonstrate_error_scenarios()
    analyze_specific_error()
    
    print("\n" + "=" * 50)
    print("🎉 增强删除功能测试完成")
    print("=" * 50)
    
    print("\n📊 新增功能:")
    print("✅ 多层次删除尝试")
    print("   - 直接删除 → 移除只读 → 重命名删除 → 强制删除")
    
    print("\n✅ 智能错误诊断")
    print("   - 检查文件属性（只读、权限等）")
    print("   - 检查文件占用状态")
    print("   - 检查路径长度限制")
    
    print("\n✅ 详细解决建议")
    print("   - 针对不同错误类型提供具体建议")
    print("   - 包含操作步骤和替代方案")
    
    print("\n💡 特别针对您的问题:")
    print("- 网络驱动器文件删除优化")
    print("- 视频文件占用检测和处理")
    print("- 权限问题的自动修复尝试")
    print("- 详细的错误分析和建议")
    
    print("\n🎯 现在遇到删除错误时:")
    print("- 程序会自动尝试多种删除方法")
    print("- 显示详细的错误原因分析")
    print("- 提供具体的解决步骤建议")
    print("- 大大提高删除成功率")
