#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证标签修复
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_verify_tag_fix():
    """快速验证标签修复"""
    print("快速验证标签修复")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="quick_tag_fix_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        for i, size in enumerate([1024, 2048, 1024]):
            file_path = os.path.join(test_dir, f"test_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * size)
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        # 显示原始结果
        app.display_results(app.current_results)
        root.update()
        
        # 执行排序
        print(f"执行按大小排序...")
        app.sort_results_by_size(app.current_results)
        root.update()
        
        # 检查排序后的标签
        sorted_tags = [tag for tag in app.text_area.tag_names() if tag.startswith("file_")]
        print(f"排序后文件标签数量: {len(sorted_tags)}")
        
        success = True
        
        for i, tag in enumerate(sorted_tags):
            try:
                color = app.text_area.tag_cget(tag, "foreground")
                underline = app.text_area.tag_cget(tag, "underline")
                ranges = app.text_area.tag_ranges(tag)
                
                print(f"标签{i+1}: {tag}")
                print(f"  样式: 颜色={color}, 下划线={underline}")
                
                if ranges:
                    text = app.text_area.get(ranges[0], ranges[1])
                    print(f"  文本: '{text}'")
                    print(f"  范围: {ranges[0]} 到 {ranges[1]}")
                    
                    if color == "blue" and underline == "1":
                        print(f"  ✅ 标签{i+1}样式正确")
                    else:
                        print(f"  ❌ 标签{i+1}样式错误")
                        success = False
                else:
                    print(f"  ❌ 标签{i+1}没有范围")
                    success = False
                    
            except Exception as e:
                print(f"  ❌ 检查标签{i+1}时出错: {str(e)}")
                success = False
        
        root.destroy()
        return success
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        return False
    
    finally:
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except:
            pass

if __name__ == "__main__":
    success = quick_verify_tag_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 标签修复验证成功！")
        print("✅ 排序后文件路径标签有正确的范围")
        print("✅ 标签样式配置正确（蓝色+下划线）")
        print("✅ 文件路径应该显示蓝色下划线")
    else:
        print("⚠️ 标签修复验证失败")
        print("❌ 排序后文件路径标签仍有问题")
    
    print("=" * 50)
    
    print("\n🔧 修复内容:")
    print("问题: 标签添加时机错误")
    print("   - 原来在插入按钮和换行符之后添加标签")
    print("   - 导致path_start和path_end范围无效")
    
    print("\n解决: 调整标签添加时机")
    print("   - 在插入文件路径后立即添加标签")
    print("   - 确保path_start和path_end范围有效")
    print("   - 标签能正确覆盖文件路径文本")
    
    print("\n💡 现在的处理流程:")
    print("1. 插入'文件路径: '")
    print("2. 记录开始位置 path_start")
    print("3. 插入文件路径")
    print("4. 记录结束位置 path_end")
    print("5. 立即添加标签和样式")
    print("6. 然后插入按钮和换行符")
    
    print("\n🎯 预期效果:")
    print("- 排序后文件路径显示蓝色下划线")
    print("- 点击文件路径可以预览文件")
    print("- 鼠标悬停显示手型光标")
    print("- 所有交互功能正常工作")
