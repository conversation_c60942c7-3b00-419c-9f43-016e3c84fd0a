#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除/移除文件后的位置保持功能
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scroll_position_preservation():
    """测试滚动位置保持功能"""
    print("测试删除/移除文件后的位置保持功能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="scroll_position_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建大量测试文件来模拟需要滚动的场景
        print("创建大量测试文件...")
        
        all_files = []
        # 创建10个不同大小的组，每组3个文件
        for i in range(10):
            size_kb = (i + 1) * 2  # 2KB, 4KB, 6KB, ...
            for j in range(3):
                file_path = os.path.join(test_dir, f"group{i:02d}_file{j}.jpg")
                with open(file_path, 'w') as f:
                    f.write('A' * (size_kb * 1024))
                all_files.append(file_path)
        
        print(f"创建了 {len(all_files)} 个测试文件，10个组")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"\n执行搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        # 测试1：测试find_nearby_file方法
        print(f"\n=== 测试1: find_nearby_file方法 ===")
        
        # 测试在同组中找到附近文件
        first_size = next(iter(app.current_results.keys()))
        first_files = app.current_results[first_size]
        
        if len(first_files) >= 2:
            deleted_file = first_files[0]
            nearby_file = app.find_nearby_file(deleted_file, first_size)
            
            print(f"删除文件: {os.path.basename(deleted_file)}")
            print(f"找到的附近文件: {os.path.basename(nearby_file) if nearby_file else 'None'}")
            
            if nearby_file and nearby_file in first_files and nearby_file != deleted_file:
                print("✅ 在同组中找到了正确的附近文件")
            else:
                print("❌ 未能在同组中找到正确的附近文件")
        
        # 测试组被删除时找到其他组的文件
        if len(app.current_results) >= 2:
            sizes = list(app.current_results.keys())
            test_size = sizes[0]
            test_files = app.current_results[test_size]
            
            # 模拟删除整个组
            backup_group = app.current_results[test_size].copy()
            del app.current_results[test_size]
            
            nearby_file = app.find_nearby_file(test_files[0], test_size)
            
            print(f"\n组被删除时:")
            print(f"删除的组大小: {test_size}")
            print(f"找到的附近文件: {os.path.basename(nearby_file) if nearby_file else 'None'}")
            
            if nearby_file:
                # 检查附近文件是否在其他组中
                found_in_other_group = False
                for size, files in app.current_results.items():
                    if nearby_file in files:
                        found_in_other_group = True
                        print(f"附近文件在组 {size} 中")
                        break
                
                if found_in_other_group:
                    print("✅ 在其他组中找到了正确的附近文件")
                else:
                    print("❌ 附近文件不在任何组中")
            else:
                print("❌ 未找到附近文件")
            
            # 恢复组
            app.current_results[test_size] = backup_group
        
        # 测试2：测试scroll_to_file方法
        print(f"\n=== 测试2: scroll_to_file方法 ===")
        
        # 显示结果
        app.display_results(app.current_results)
        
        # 等待界面更新
        root.update()
        time.sleep(0.1)
        
        # 获取一个中间位置的文件
        all_result_files = []
        for files in app.current_results.values():
            all_result_files.extend(files)
        
        if len(all_result_files) >= 5:
            middle_file = all_result_files[len(all_result_files) // 2]
            
            print(f"测试滚动到文件: {os.path.basename(middle_file)}")
            
            # 先滚动到顶部
            app.text_area.see("1.0")
            root.update()
            
            # 记录滚动前的位置
            before_scroll = app.text_area.yview()
            
            # 滚动到目标文件
            app.scroll_to_file(middle_file)
            root.update()
            time.sleep(0.1)
            
            # 记录滚动后的位置
            after_scroll = app.text_area.yview()
            
            print(f"滚动前位置: {before_scroll[0]:.3f}")
            print(f"滚动后位置: {after_scroll[0]:.3f}")
            
            if after_scroll[0] != before_scroll[0]:
                print("✅ 滚动位置发生了变化")
            else:
                print("❌ 滚动位置没有变化")
        
        # 测试3：测试删除操作的位置保持
        print(f"\n=== 测试3: 删除操作的位置保持 ===")
        
        # 选择一个中间的组进行测试
        sizes = sorted(app.current_results.keys())
        if len(sizes) >= 5:
            middle_size = sizes[len(sizes) // 2]
            middle_files = app.current_results[middle_size]
            
            if len(middle_files) >= 2:
                # 先滚动到该组附近
                app.scroll_to_file(middle_files[0])
                root.update()
                time.sleep(0.1)
                
                # 记录删除前的位置
                before_delete = app.text_area.yview()
                
                # 删除一个文件
                file_to_delete = middle_files[0]
                print(f"删除文件: {os.path.basename(file_to_delete)}")
                
                # 模拟删除操作（不实际删除文件）
                app.current_results[middle_size].remove(file_to_delete)
                
                # 调用带位置保持的显示更新
                focus_file = app.find_nearby_file(file_to_delete, middle_size)
                app.display_results(app.current_results, focus_file=focus_file, preserve_scroll=True)
                
                root.update()
                time.sleep(0.2)
                
                # 记录删除后的位置
                after_delete = app.text_area.yview()
                
                print(f"删除前位置: {before_delete[0]:.3f}")
                print(f"删除后位置: {after_delete[0]:.3f}")
                print(f"位置差异: {abs(after_delete[0] - before_delete[0]):.3f}")
                
                # 如果位置差异很小，说明位置保持成功
                if abs(after_delete[0] - before_delete[0]) < 0.1:
                    print("✅ 删除后位置保持成功")
                else:
                    print("❌ 删除后位置发生了较大变化")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def demonstrate_user_experience_improvement():
    """演示用户体验改进"""
    print("\n" + "=" * 50)
    print("用户体验改进演示")
    print("=" * 50)
    
    print("🎯 优化前的用户体验:")
    print("1. 用户在一个有100个重复文件组的列表中工作")
    print("2. 用户滚动到第50个组，删除其中一个文件")
    print("3. ❌ 界面自动回到列表顶部")
    print("4. 用户需要重新滚动找到第50个组的位置")
    print("5. 重复这个过程，每次删除都要重新定位")
    print("6. 用户体验：非常繁琐，效率低下")
    
    print("\n🚀 优化后的用户体验:")
    print("1. 用户在一个有100个重复文件组的列表中工作")
    print("2. 用户滚动到第50个组，删除其中一个文件")
    print("3. ✅ 界面保持在第50个组附近的位置")
    print("4. 用户可以继续处理附近的其他组")
    print("5. 工作流程连续，无需重复定位")
    print("6. 用户体验：流畅高效，符合预期")
    
    print("\n💡 技术实现亮点:")
    print("✅ 智能焦点文件选择")
    print("   - 优先选择同组中的其他文件")
    print("   - 组被删除时选择最接近的组")
    print("   - 确保焦点文件始终有效")
    
    print("\n✅ 滚动位置保持")
    print("   - 保存删除前的滚动位置")
    print("   - 界面更新后恢复位置")
    print("   - 结合焦点文件实现精确定位")
    
    print("\n✅ 适用于所有操作")
    print("   - 单个文件删除")
    print("   - 单个文件移除")
    print("   - 批量文件删除")
    print("   - 批量文件移除")
    
    print("\n🎯 特别适用的场景:")
    print("- 大量重复文件组的清理工作")
    print("- 按时长搜索的视频文件整理")
    print("- 需要逐个检查和删除的精细操作")
    print("- 长时间的文件管理任务")

if __name__ == "__main__":
    test_scroll_position_preservation()
    demonstrate_user_experience_improvement()
    
    print("\n" + "=" * 50)
    print("🎉 位置保持功能测试完成")
    print("=" * 50)
    
    print("\n📊 新增功能:")
    print("✅ 智能焦点文件查找")
    print("   - find_nearby_file(): 找到删除文件附近的合适文件")
    print("   - 优先同组文件，其次最接近的组")
    
    print("\n✅ 精确位置滚动")
    print("   - scroll_to_file(): 滚动到指定文件位置")
    print("   - 自动居中显示，包含组标题")
    
    print("\n✅ 滚动位置保持")
    print("   - preserve_scroll参数: 保持删除前的滚动位置")
    print("   - 结合焦点文件实现精确定位")
    
    print("\n✅ 全面集成")
    print("   - 单个删除/移除: 保持位置")
    print("   - 批量删除/移除: 保持位置")
    print("   - 所有界面更新: 智能位置管理")
    
    print("\n💡 用户体验提升:")
    print("- 消除了重复滚动定位的烦恼")
    print("- 提高了大量文件处理的效率")
    print("- 保持了操作的连续性和流畅性")
    print("- 符合用户的直觉和预期")
    
    print("\n🎯 解决的核心问题:")
    print("问题: 删除/移除文件后列表回到顶部")
    print("解决: 智能保持用户的工作位置")
    print("结果: 大幅提升文件管理效率")
