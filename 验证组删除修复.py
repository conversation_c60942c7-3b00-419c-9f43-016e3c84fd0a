#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证组自动删除功能的修复
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_group_removal_fix():
    """测试组自动删除功能的修复"""
    print("验证组自动删除功能的修复")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="group_removal_fix_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        # 组1：2个文件（删除1个后应该删除组）
        group1_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"group1_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * 1024)
            group1_files.append(file_path)
        
        # 组2：3个文件（删除1个后应该保留组）
        group2_files = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"group2_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('B' * 2048)
            group2_files.append(file_path)
        
        print(f"创建了2个测试组，共 {len(group1_files + group2_files)} 个文件")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"\n执行搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 测试1：删除导致组删除的文件
        print(f"\n=== 测试1: 删除导致组删除的文件 ===")
        
        if 1024 in app.current_results and len(app.current_results[1024]) == 2:
            file_to_delete = group1_files[0]
            print(f"删除文件: {os.path.basename(file_to_delete)}")
            print(f"删除前组1有 {len(app.current_results[1024])} 个文件")
            
            # 执行删除
            app.delete_file(file_to_delete, 1024)
            
            # 检查组是否被删除
            if 1024 not in app.current_results:
                print("✅ 组1已被正确删除")
            else:
                remaining = len(app.current_results[1024])
                print(f"❌ 组1仍存在，剩余 {remaining} 个文件（应该被删除）")
                
                # 检查界面显示
                content = app.text_area.get("1.0", tk.END)
                if "文件大小: 1024" in content:
                    print("❌ 界面仍显示该组")
                else:
                    print("✅ 界面已不显示该组（数据问题）")
        
        # 测试2：删除不导致组删除的文件
        print(f"\n=== 测试2: 删除不导致组删除的文件 ===")
        
        if 2048 in app.current_results and len(app.current_results[2048]) == 3:
            file_to_delete = group2_files[0]
            print(f"删除文件: {os.path.basename(file_to_delete)}")
            print(f"删除前组2有 {len(app.current_results[2048])} 个文件")
            
            # 执行删除
            app.delete_file(file_to_delete, 2048)
            
            # 检查组是否保留
            if 2048 in app.current_results:
                remaining = len(app.current_results[2048])
                print(f"✅ 组2正确保留，剩余 {remaining} 个文件")
            else:
                print(f"❌ 组2被错误删除")
        
        # 测试3：继续删除组2的文件直到只剩1个
        print(f"\n=== 测试3: 继续删除直到只剩1个文件 ===")
        
        if 2048 in app.current_results and len(app.current_results[2048]) == 2:
            file_to_delete = app.current_results[2048][0]
            print(f"删除文件: {os.path.basename(file_to_delete)}")
            print(f"删除前组2有 {len(app.current_results[2048])} 个文件")
            
            # 执行删除
            app.delete_file(file_to_delete, 2048)
            
            # 检查组是否被删除
            if 2048 not in app.current_results:
                print("✅ 组2已被正确删除")
            else:
                remaining = len(app.current_results[2048])
                print(f"❌ 组2仍存在，剩余 {remaining} 个文件（应该被删除）")
        
        # 最终状态检查
        print(f"\n=== 最终状态检查 ===")
        final_groups = len(app.current_results)
        print(f"最终组数: {final_groups}")
        
        # 检查是否有单文件组
        single_file_groups = []
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
            if len(files) == 1:
                single_file_groups.append(size)
        
        if not single_file_groups:
            print("✅ 没有发现单文件组")
        else:
            print(f"❌ 发现 {len(single_file_groups)} 个单文件组: {single_file_groups}")
        
        # 检查界面显示
        print(f"\n=== 界面显示检查 ===")
        content = app.text_area.get("1.0", tk.END)
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        group_lines = [line for line in lines if "文件大小:" in line]
        
        print(f"界面显示的组数: {len(group_lines)}")
        print(f"数据中的组数: {final_groups}")
        
        if len(group_lines) == final_groups:
            print("✅ 界面显示与数据一致")
        else:
            print("❌ 界面显示与数据不一致")
            print("界面显示的组:")
            for line in group_lines:
                print(f"  {line}")
        
        # 总结测试结果
        print(f"\n=== 测试结果总结 ===")
        
        success = True
        
        # 检查是否还有单文件组
        if single_file_groups:
            print("❌ 存在单文件组，组自动删除功能有问题")
            success = False
        
        # 检查界面一致性
        if len(group_lines) != final_groups:
            print("❌ 界面显示不一致")
            success = False
        
        if success:
            print("🎉 组自动删除功能工作正常")
        else:
            print("⚠️ 组自动删除功能仍有问题")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def analyze_optimization_vs_correctness():
    """分析性能优化与功能正确性的平衡"""
    print("\n" + "=" * 50)
    print("性能优化与功能正确性的平衡分析")
    print("=" * 50)
    
    print("🔍 问题模式分析:")
    print("每次进行速度优化时，都会出现组自动删除失效的问题")
    print("这反映了一个重要的软件开发原则冲突：")
    print("  - 性能优化 vs 功能正确性")
    print("  - 复杂优化 vs 简单可靠")
    
    print("\n❌ 失败的优化尝试:")
    print("1. 高效组移除方法")
    print("   - 尝试通过解析界面文本找到文件")
    print("   - 依赖界面状态与数据状态的同步")
    print("   - 在数据已修改但界面未更新时失效")
    
    print("\n✅ 正确的解决方案:")
    print("1. 优先保证功能正确性")
    print("   - 当检测到组删除时，使用可靠的完全更新")
    print("   - 虽然可能稍慢，但确保功能正确")
    
    print("\n2. 性能优化的边界")
    print("   - 只在不影响核心功能的情况下优化")
    print("   - 复杂的优化需要更多的测试验证")
    print("   - 简单可靠 > 复杂高效")
    
    print("\n💡 经验教训:")
    print("1. 核心功能的正确性不能妥协")
    print("2. 性能优化应该是渐进式的")
    print("3. 每次优化都需要完整的功能测试")
    print("4. 复杂的优化往往引入新的bug")
    
    print("\n🎯 最佳实践:")
    print("1. 先确保功能正确，再考虑性能")
    print("2. 优化时保留原有的可靠路径")
    print("3. 新的优化路径失败时能自动降级")
    print("4. 充分测试边界情况和异常场景")

if __name__ == "__main__":
    test_group_removal_fix()
    analyze_optimization_vs_correctness()
    
    print("\n" + "=" * 50)
    print("🔧 组删除修复验证完成")
    print("=" * 50)
    
    print("\n📊 修复内容:")
    print("✅ 移除了不可靠的高效组移除方法")
    print("✅ 恢复了可靠的完全更新机制")
    print("✅ 确保组删除时功能正确性")
    
    print("\n🎯 修复原理:")
    print("问题: 高效组移除依赖界面解析，不可靠")
    print("解决: 直接使用完全更新，确保正确性")
    print("结果: 功能正确性得到保障")
    
    print("\n⚖️ 性能与正确性的平衡:")
    print("- 组删除场景相对较少，性能影响有限")
    print("- 功能正确性比微小的性能提升更重要")
    print("- 用户更关心功能是否正确工作")
    print("- 可靠性是用户体验的基础")
