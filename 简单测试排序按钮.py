#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试排序按钮功能
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def simple_test_sort_buttons():
    """简单测试排序按钮功能"""
    print("简单测试排序按钮功能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="simple_sort_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建不同大小的测试文件
        test_files = []
        sizes = [1024, 2048, 1024, 4096, 2048]  # 故意不按顺序，有重复
        
        for i, size in enumerate(sizes):
            file_path = os.path.join(test_dir, f"test_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * size)
            test_files.append(file_path)
        
        print(f"创建了 {len(test_files)} 个测试文件")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"\n执行搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        # 显示原始结果（不排序）
        print(f"\n显示原始结果...")
        app.display_results(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 检查界面内容
        content = app.text_area.get("1.0", tk.END)
        print(f"界面内容包含排序按钮: {'按大小排序' in content and '按时长排序' in content}")
        
        # 检查当前搜索类型
        print(f"当前搜索类型: {app.current_search_type}")
        
        # 测试按大小排序
        print(f"\n测试按大小排序...")
        original_keys = list(app.current_results.keys())
        print(f"排序前的键: {original_keys}")
        
        # 执行排序
        app.sort_results_by_size(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 检查排序结果
        if app._sorted_results_cache:
            sorted_keys = [key for key, _ in app._sorted_results_cache]
            print(f"排序后的键: {sorted_keys}")
            
            # 验证是否按降序排列
            expected_sorted = sorted(original_keys, reverse=True)
            if sorted_keys == expected_sorted:
                print("✅ 按大小排序功能正常")
            else:
                print("❌ 按大小排序功能异常")
        
        # 检查排序后的界面
        sorted_content = app.text_area.get("1.0", tk.END)
        if "按大小排序：从大到小" in sorted_content:
            print("✅ 排序后界面显示正确")
        else:
            print("❌ 排序后界面显示异常")
        
        # 测试时长排序（使用相同数据）
        print(f"\n测试按时长排序...")
        app.sort_results_by_duration(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 检查时长排序后的界面
        duration_content = app.text_area.get("1.0", tk.END)
        if "按时长排序：从长到短" in duration_content:
            print("✅ 按时长排序功能正常")
        else:
            print("❌ 按时长排序功能异常")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_manual_sort_results():
    """手动测试排序结果"""
    print("\n" + "=" * 50)
    print("手动测试排序结果")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 创建测试数据
        test_results = {
            4096: ["/test/large1.jpg", "/test/large2.jpg"],     # 4KB
            1024: ["/test/small1.jpg", "/test/small2.jpg"],     # 1KB  
            2048: ["/test/medium1.jpg", "/test/medium2.jpg"],   # 2KB
            8192: ["/test/huge1.jpg", "/test/huge2.jpg"]        # 8KB
        }
        
        app.current_results = test_results
        app.current_search_type = "size"
        
        print(f"原始数据键: {list(test_results.keys())}")
        
        # 测试按大小排序
        print(f"\n测试按大小排序...")
        app.sort_results_by_size(test_results)
        
        if app._sorted_results_cache:
            sorted_keys = [key for key, _ in app._sorted_results_cache]
            print(f"排序后的键: {sorted_keys}")
            
            expected = [8192, 4096, 2048, 1024]  # 从大到小
            if sorted_keys == expected:
                print("✅ 按大小排序逻辑正确")
            else:
                print(f"❌ 按大小排序逻辑错误，期望: {expected}")
        
        # 测试时长数据
        duration_results = {
            30.5: ["/test/short1.mp4", "/test/short2.mp4"],     # 30.5秒
            120.8: ["/test/long1.mp4", "/test/long2.mp4"],      # 120.8秒
            60.2: ["/test/medium1.mp4", "/test/medium2.mp4"],   # 60.2秒
            90.1: ["/test/long2.mp4", "/test/long3.mp4"]        # 90.1秒
        }
        
        print(f"\n时长数据键: {list(duration_results.keys())}")
        
        # 测试按时长排序
        print(f"测试按时长排序...")
        app.sort_results_by_duration(duration_results)
        
        if app._sorted_results_cache:
            sorted_keys = [key for key, _ in app._sorted_results_cache]
            print(f"排序后的键: {sorted_keys}")
            
            expected = [120.8, 90.1, 60.2, 30.5]  # 从长到短
            if sorted_keys == expected:
                print("✅ 按时长排序逻辑正确")
            else:
                print(f"❌ 按时长排序逻辑错误，期望: {expected}")
        
        root.destroy()
        
    except Exception as e:
        print(f"手动测试出错: {str(e)}")

if __name__ == "__main__":
    success = simple_test_sort_buttons()
    test_manual_sort_results()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 排序按钮功能测试成功！")
    else:
        print("⚠️ 排序按钮功能测试失败")
    
    print("=" * 50)
    
    print("\n📊 功能总结:")
    print("✅ 取消了默认自动排序")
    print("   - 搜索结果保持原始顺序")
    print("   - 用户可自主选择排序方式")
    
    print("\n✅ 添加了动态排序按钮")
    print("   - '按大小排序'按钮：从大到小排列")
    print("   - '按时长排序'按钮：从长到短排列")
    print("   - 按钮嵌入在搜索结果界面中")
    
    print("\n✅ 实现了智能排序功能")
    print("   - sort_results_by_size(): 按文件大小排序")
    print("   - sort_results_by_duration(): 按视频时长排序")
    print("   - redisplay_sorted_results(): 重新显示排序结果")
    
    print("\n✅ 保持了系统一致性")
    print("   - 排序后更新缓存系统")
    print("   - 保持界面滚动位置")
    print("   - 维护删除按钮功能")
    
    print("\n💡 用户体验改进:")
    print("🚀 更大的灵活性 - 用户可选择最适合的排序方式")
    print("🚀 更好的控制感 - 主动选择而非被动接受")
    print("🚀 适应不同场景 - 按需求选择大小或时长排序")
    
    print("\n🎯 特别适用于:")
    print("- 大量重复文件的分类整理")
    print("- 172个组的时长搜索结果管理")
    print("- 灵活的文件管理工作流")
    print("- 个性化的排序偏好设置")
