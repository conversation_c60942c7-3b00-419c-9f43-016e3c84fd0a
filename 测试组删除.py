#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复文件查找器的组自动删除功能
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_group_auto_removal():
    """测试组自动删除功能"""
    print("测试重复文件组自动删除功能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="group_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        # 组1：3个1KB文件
        content_1kb = 'A' * 1024
        group1_files = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"group1_file_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(content_1kb)
            group1_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (1KB)")
        
        # 组2：2个2KB文件
        content_2kb = 'B' * 2048
        group2_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"group2_file_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(content_2kb)
            group2_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (2KB)")
        
        # 组3：4个4KB文件
        content_4kb = 'C' * 4096
        group3_files = []
        for i in range(4):
            file_path = os.path.join(test_dir, f"group3_file_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(content_4kb)
            group3_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (4KB)")
        
        # 导入并测试应用
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 设置目录并搜索
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 手动设置搜索结果
        app.current_results = {
            1024: group1_files,
            2048: group2_files,
            4096: group3_files
        }
        
        print(f"\n初始状态:")
        print(f"组数: {len(app.current_results)}")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 测试1：删除组1中的2个文件，应该还剩1个文件，组应该被删除
        print(f"\n测试1: 删除组1中的2个文件")
        print(f"删除: {os.path.basename(group1_files[0])}")
        app.delete_file(group1_files[0], 1024)
        
        print(f"删除后组数: {len(app.current_results)}")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        print(f"删除: {os.path.basename(group1_files[1])}")
        app.delete_file(group1_files[1], 1024)
        
        print(f"删除后组数: {len(app.current_results)}")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 检查组1是否被正确删除
        if 1024 not in app.current_results:
            print("✓ 组1正确删除（只剩1个文件时自动删除）")
        else:
            print("✗ 组1未被删除，存在问题")
        
        # 测试2：删除组2中的1个文件，应该还剩1个文件，组应该被删除
        print(f"\n测试2: 删除组2中的1个文件")
        print(f"删除: {os.path.basename(group2_files[0])}")
        app.delete_file(group2_files[0], 2048)
        
        print(f"删除后组数: {len(app.current_results)}")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 检查组2是否被正确删除
        if 2048 not in app.current_results:
            print("✓ 组2正确删除（只剩1个文件时自动删除）")
        else:
            print("✗ 组2未被删除，存在问题")
        
        # 测试3：批量删除组3中的3个文件，应该还剩1个文件，组应该被删除
        print(f"\n测试3: 批量删除组3中的3个文件")
        
        # 模拟勾选文件
        for file_path in group3_files[:3]:
            if file_path in app.checkbox_vars:
                app.checkbox_vars[file_path].set(True)
        
        print(f"批量删除3个文件")
        app.delete_selected_files()
        
        print(f"删除后组数: {len(app.current_results)}")
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
        
        # 检查组3是否被正确删除
        if 4096 not in app.current_results:
            print("✓ 组3正确删除（只剩1个文件时自动删除）")
        else:
            print("✗ 组3未被删除，存在问题")
        
        # 最终检查
        print(f"\n最终结果:")
        if len(app.current_results) == 0:
            print("✓ 所有组都被正确删除，功能正常")
        else:
            print(f"✗ 还有 {len(app.current_results)} 个组未被删除")
            for size, files in app.current_results.items():
                print(f"  大小 {size}: {len(files)} 个文件")
        
        # 测试4：测试移出列表功能
        print(f"\n测试4: 测试移出列表功能")
        
        # 重新创建一个小组进行测试
        test_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"test_remove_{i}.txt")
            with open(file_path, 'w') as f:
                f.write('D' * 512)
            test_files.append(file_path)
        
        app.current_results[512] = test_files
        print(f"创建测试组: 大小512, {len(test_files)}个文件")
        
        # 移出一个文件
        print(f"移出文件: {os.path.basename(test_files[0])}")
        app.remove_single_file(test_files[0], 512)
        
        if 512 not in app.current_results:
            print("✓ 移出功能正常（只剩1个文件时自动删除组）")
        else:
            print("✗ 移出功能存在问题")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    test_dir = tempfile.mkdtemp(prefix="edge_test_")
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试空结果
        app.current_results = {}
        print("测试空结果集...")
        app.display_results(app.current_results)
        print("✓ 空结果集处理正常")
        
        # 测试单文件组（不应该出现，但要确保程序不崩溃）
        single_file = os.path.join(test_dir, "single.txt")
        with open(single_file, 'w') as f:
            f.write('test')
        
        app.current_results = {100: [single_file]}
        print("测试单文件组...")
        
        # 删除这个单文件，组应该被删除
        app.delete_file(single_file, 100)
        
        if 100 not in app.current_results:
            print("✓ 单文件组正确删除")
        else:
            print("✗ 单文件组删除失败")
        
        root.destroy()
        
    except Exception as e:
        print(f"边界测试出错: {str(e)}")
    
    finally:
        try:
            shutil.rmtree(test_dir)
        except:
            pass

if __name__ == "__main__":
    test_group_auto_removal()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    print("\n功能说明:")
    print("1. 当重复文件组只剩下1个文件时，该组应该自动从结果中删除")
    print("2. 这适用于所有删除操作：单个删除、批量删除、移出列表")
    print("3. 界面应该正确更新，不显示只有1个文件的'重复'组")
    print("4. 缓存机制应该正确处理组的删除")
