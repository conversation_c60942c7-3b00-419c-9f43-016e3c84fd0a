#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试预览优化功能
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def simple_test():
    """简单测试"""
    print("简单预览优化测试")
    print("=" * 40)
    
    try:
        # 导入应用
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        print("✓ 应用创建成功")
        
        # 测试预览状态设置
        app.is_playing = True
        app._current_preview = "test_file.mp4"
        
        print(f"设置预览状态: is_playing={app.is_playing}")
        print(f"当前预览文件: {app._current_preview}")
        
        # 测试停止预览功能
        print("\n测试停止预览功能...")
        app.stop_preview()
        
        print(f"停止后状态: is_playing={app.is_playing}")
        print(f"当前预览文件: {app._current_preview}")
        
        if not app.is_playing and not app._current_preview:
            print("✓ 停止预览功能正常")
        else:
            print("✗ 停止预览功能异常")
        
        # 测试删除文件方法中的预览检查逻辑
        print("\n测试删除方法中的预览检查...")
        
        # 重新设置预览状态
        app.is_playing = True
        app._current_preview = "test_file.mp4"
        
        # 创建一个临时文件用于测试
        test_dir = tempfile.mkdtemp()
        test_file = os.path.join(test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        # 设置测试结果
        app.current_results = {100: [test_file]}
        
        print(f"删除前预览状态: is_playing={app.is_playing}")
        
        # 调用删除方法
        app.delete_file(test_file, 100)
        
        print(f"删除后预览状态: is_playing={app.is_playing}")
        
        if not app.is_playing:
            print("✓ 删除操作正确停止了预览")
        else:
            print("✗ 删除操作未能停止预览")
        
        # 清理
        shutil.rmtree(test_dir)
        root.destroy()
        
        print("\n✅ 简单测试完成")
        
    except Exception as e:
        print(f"测试出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
    
    print("\n" + "=" * 40)
    print("预览优化功能说明")
    print("=" * 40)
    
    print("\n🎯 优化目标:")
    print("解决正在预览时删除或移除文件导致刷新列表很慢的问题")
    
    print("\n✅ 已实现的优化:")
    print("1. delete_file() - 删除单个文件前停止预览")
    print("2. delete_selected_files() - 批量删除前停止预览")
    print("3. remove_selected_from_list() - 移出列表前停止预览")
    print("4. remove_single_file() - 单个移出前停止预览")
    
    print("\n🚀 优化效果:")
    print("- 避免视频预览占用系统资源影响删除操作")
    print("- 提高界面刷新速度")
    print("- 改善用户体验，操作更流畅")
    print("- 自动化处理，无需用户手动停止预览")
    
    print("\n💡 实现原理:")
    print("在执行删除/移除操作前，检查是否正在预览：")
    print("if self.is_playing or self._current_preview:")
    print("    self.stop_preview()")
    print("这样确保操作前释放视频预览占用的资源")
