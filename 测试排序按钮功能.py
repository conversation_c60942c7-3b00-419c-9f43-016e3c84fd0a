#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的排序按钮功能
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sort_buttons_functionality():
    """测试排序按钮功能"""
    print("测试新的排序按钮功能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="sort_buttons_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建混合类型的测试文件
        print("创建混合类型的测试文件...")
        
        # 创建不同大小的图片文件
        image_files = []
        for i, size_kb in enumerate([10, 20, 15, 25, 10, 20]):  # 故意不按顺序
            file_path = os.path.join(test_dir, f"image_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * (size_kb * 1024))
            image_files.append(file_path)
        
        # 创建不同时长的视频文件（模拟）
        video_files = []
        for i, duration in enumerate([30.5, 60.2, 45.8, 90.1, 30.5, 60.2]):  # 故意不按顺序
            file_path = os.path.join(test_dir, f"video_{i}.mp4")
            with open(file_path, 'w') as f:
                f.write('V' * 1024)  # 小文件，主要测试时长
            video_files.append(file_path)
        
        print(f"创建了 {len(image_files)} 个图片文件和 {len(video_files)} 个视频文件")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        app.include_videos_var.set(True)
        
        # 测试1：按大小搜索并测试排序按钮
        print(f"\n=== 测试1: 按大小搜索并测试排序按钮 ===")
        
        # 执行大小搜索
        app._search_by_size_worker()
        print(f"大小搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        # 显示结果（不默认排序）
        app.display_results(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 检查当前搜索类型
        print(f"当前搜索类型: {app.current_search_type}")
        
        if app.current_search_type == "size":
            print("✅ 正确识别为大小搜索")
        else:
            print("❌ 搜索类型识别错误")
        
        # 检查界面内容
        content = app.text_area.get("1.0", tk.END)
        if "找到以下大小相同的文件组" in content:
            print("✅ 界面显示正确的标题")
        else:
            print("❌ 界面标题显示错误")
        
        # 测试按大小排序按钮
        print(f"\n测试按大小排序按钮...")
        original_results = app.current_results.copy()
        
        # 模拟点击按大小排序按钮
        app.sort_results_by_size(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 检查排序后的内容
        sorted_content = app.text_area.get("1.0", tk.END)
        if "按大小排序：从大到小" in sorted_content:
            print("✅ 按大小排序功能正常")
        else:
            print("❌ 按大小排序功能异常")
        
        # 测试2：创建时长搜索结果并测试排序
        print(f"\n=== 测试2: 时长搜索结果并测试排序 ===")
        
        # 手动创建时长搜索结果
        duration_results = {
            30.5: [video_files[0], video_files[4]],  # 30.5秒的视频
            60.2: [video_files[1], video_files[5]],  # 60.2秒的视频
            45.8: [video_files[2]],                  # 45.8秒的视频（单个文件，会被清理）
            90.1: [video_files[3]]                   # 90.1秒的视频（单个文件，会被清理）
        }
        
        app.current_results = duration_results
        app.current_search_type = "duration"
        
        # 显示时长搜索结果
        app.display_results(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 检查时长搜索的界面
        duration_content = app.text_area.get("1.0", tk.END)
        if "找到以下时长相同的文件组" in duration_content:
            print("✅ 时长搜索界面显示正确")
        else:
            print("❌ 时长搜索界面显示错误")
        
        # 测试按时长排序按钮
        print(f"\n测试按时长排序按钮...")
        
        # 模拟点击按时长排序按钮
        app.sort_results_by_duration(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 检查排序后的内容
        duration_sorted_content = app.text_area.get("1.0", tk.END)
        if "按时长排序：从长到短" in duration_sorted_content:
            print("✅ 按时长排序功能正常")
        else:
            print("❌ 按时长排序功能异常")
        
        # 测试3：验证排序缓存功能
        print(f"\n=== 测试3: 验证排序缓存功能 ===")
        
        # 检查缓存是否正确更新
        if hasattr(app, '_sorted_results_cache') and app._sorted_results_cache:
            print("✅ 排序缓存已更新")
            
            # 检查缓存的搜索类型
            if app._cache_search_type == True:  # True表示时长搜索
                print("✅ 缓存搜索类型正确（时长搜索）")
            else:
                print("❌ 缓存搜索类型错误")
        else:
            print("❌ 排序缓存未更新")
        
        # 测试4：测试排序按钮的交互性
        print(f"\n=== 测试4: 测试排序按钮的交互性 ===")
        
        # 创建一个包含多种大小的结果
        mixed_results = {
            1024: ["/test/small1.jpg", "/test/small2.jpg"],      # 1KB
            2048: ["/test/medium1.jpg", "/test/medium2.jpg"],    # 2KB
            512: ["/test/tiny1.jpg", "/test/tiny2.jpg"],         # 0.5KB
            4096: ["/test/large1.jpg", "/test/large2.jpg"]       # 4KB
        }
        
        app.current_results = mixed_results
        app.current_search_type = "size"
        
        # 显示结果
        app.display_results(app.current_results)
        root.update()
        time.sleep(0.1)
        
        print("原始顺序的键:", list(mixed_results.keys()))
        
        # 测试按大小排序
        app.sort_results_by_size(app.current_results)
        root.update()
        time.sleep(0.1)
        
        # 检查排序后的顺序
        if app._sorted_results_cache:
            sorted_keys = [key for key, _ in app._sorted_results_cache]
            print("排序后的键:", sorted_keys)
            
            # 验证是否按降序排列
            if sorted_keys == sorted(sorted_keys, reverse=True):
                print("✅ 按大小排序结果正确（降序）")
            else:
                print("❌ 按大小排序结果错误")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def demonstrate_new_functionality():
    """演示新功能"""
    print("\n" + "=" * 50)
    print("新排序按钮功能演示")
    print("=" * 50)
    
    print("🎯 功能改进:")
    print("❌ 改进前: 搜索后自动按默认方式排序")
    print("   - 用户无法选择排序方式")
    print("   - 固定的排序逻辑")
    print("   - 无法灵活调整显示顺序")
    
    print("\n✅ 改进后: 用户可自主选择排序方式")
    print("   - 搜索后显示原始顺序")
    print("   - 提供'按大小排序'和'按时长排序'按钮")
    print("   - 用户可根据需要选择排序方式")
    print("   - 支持动态切换排序方式")
    
    print("\n🔧 技术实现:")
    print("✅ 取消默认排序")
    print("   - display_results不再自动排序")
    print("   - 保持搜索结果的原始顺序")
    
    print("\n✅ 动态排序按钮")
    print("   - 在结果标题下方显示排序按钮")
    print("   - 按大小排序：从大到小排列")
    print("   - 按时长排序：从长到短排列")
    
    print("\n✅ 智能界面更新")
    print("   - 排序后重新显示结果")
    print("   - 保持滚动位置")
    print("   - 更新排序缓存")
    
    print("\n💡 用户体验提升:")
    print("🚀 更大的灵活性")
    print("   - 用户可以选择最适合的排序方式")
    print("   - 支持在不同排序方式间切换")
    
    print("\n🚀 更好的控制感")
    print("   - 用户主动选择而非被动接受")
    print("   - 清晰的排序状态显示")
    
    print("\n🚀 适应不同场景")
    print("   - 按大小排序：适合清理大文件")
    print("   - 按时长排序：适合整理视频文件")
    print("   - 原始顺序：保持搜索时的发现顺序")

if __name__ == "__main__":
    test_sort_buttons_functionality()
    demonstrate_new_functionality()
    
    print("\n" + "=" * 50)
    print("🎉 排序按钮功能测试完成")
    print("=" * 50)
    
    print("\n📊 新增功能:")
    print("✅ 取消默认排序")
    print("   - 搜索结果保持原始顺序")
    print("   - 用户可自主选择排序方式")
    
    print("\n✅ 动态排序按钮")
    print("   - 按大小排序按钮")
    print("   - 按时长排序按钮")
    print("   - 嵌入在结果界面中")
    
    print("\n✅ 智能排序功能")
    print("   - sort_results_by_size(): 按大小降序排列")
    print("   - sort_results_by_duration(): 按时长降序排列")
    print("   - redisplay_sorted_results(): 重新显示排序结果")
    
    print("\n✅ 缓存系统集成")
    print("   - 排序后更新缓存")
    print("   - 保持缓存一致性")
    print("   - 提高后续操作性能")
    
    print("\n💡 用户体验改进:")
    print("- 更大的排序灵活性")
    print("- 清晰的排序状态显示")
    print("- 适应不同的使用场景")
    print("- 保持界面响应性")
    
    print("\n🎯 特别适用场景:")
    print("- 大量重复文件的分类整理")
    print("- 按不同标准查看搜索结果")
    print("- 灵活的文件管理工作流")
    print("- 个性化的排序偏好")
