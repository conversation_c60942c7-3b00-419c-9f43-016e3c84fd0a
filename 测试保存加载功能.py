#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保存和加载功能对时长搜索的支持
"""

import os
import sys
import tempfile
import shutil
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_save_load_functionality():
    """测试保存和加载功能"""
    print("测试保存和加载功能对时长搜索的支持")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试1：测试搜索类型检测功能
        print("=== 测试1: 搜索类型检测功能 ===")
        
        # 模拟按大小搜索的结果
        size_results = {
            1024: ["/test/file1.jpg", "/test/file2.jpg"],
            2048: ["/test/file3.jpg", "/test/file4.jpg"]
        }
        
        app.current_results = size_results
        search_type, filename = app._detect_search_type_and_filename()
        print(f"大小搜索检测结果: 类型={search_type}, 文件名={filename}")
        
        if search_type == "size" and filename == "same_size_files.json":
            print("✅ 大小搜索类型检测正确")
        else:
            print("❌ 大小搜索类型检测错误")
        
        # 模拟按时长搜索的结果
        duration_results = {
            30.5: ["/test/video1.mp4", "/test/video2.mp4"],
            60.2: ["/test/video3.mp4", "/test/video4.mp4"]
        }
        
        app.current_results = duration_results
        search_type, filename = app._detect_search_type_and_filename()
        print(f"时长搜索检测结果: 类型={search_type}, 文件名={filename}")
        
        if search_type == "duration" and filename == "same_duration_files.json":
            print("✅ 时长搜索类型检测正确")
        else:
            print("❌ 时长搜索类型检测错误")
        
        # 测试2：测试保存功能的数据格式
        print(f"\n=== 测试2: 保存功能的数据格式 ===")
        
        # 创建临时保存文件
        temp_save_file = tempfile.mktemp(suffix=".json")
        
        # 模拟保存过程（手动构造保存数据）
        app.current_directory = "/test/directory"
        app.current_results = duration_results
        
        # 检测搜索类型
        search_type, _ = app._detect_search_type_and_filename()
        
        # 构造保存数据
        save_data = {
            "directory": app.current_directory,
            "timestamp": "2024-01-01T12:00:00",
            "search_type": search_type,
            "results": app.current_results
        }
        
        # 保存到文件
        with open(temp_save_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"保存的数据格式:")
        print(f"  directory: {save_data['directory']}")
        print(f"  search_type: {save_data['search_type']}")
        print(f"  results: {len(save_data['results'])} 个组")
        
        if save_data['search_type'] == 'duration':
            print("✅ 保存数据包含正确的搜索类型信息")
        else:
            print("❌ 保存数据搜索类型信息错误")
        
        # 测试3：测试加载功能
        print(f"\n=== 测试3: 加载功能 ===")
        
        # 清空当前结果
        app.current_results = {}
        
        # 手动加载数据（模拟load_results的核心逻辑）
        with open(temp_save_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        loaded_directory = loaded_data.get("directory", "")
        loaded_search_type = loaded_data.get("search_type", "size")
        loaded_results = loaded_data.get("results", {})
        
        print(f"加载的数据:")
        print(f"  directory: {loaded_directory}")
        print(f"  search_type: {loaded_search_type}")
        print(f"  results: {len(loaded_results)} 个组")
        
        # 处理加载的结果（转换键类型）
        processed_results = {}
        for size, files in loaded_results.items():
            try:
                if isinstance(size, str) and size.replace('.', '').isdigit():
                    numeric_key = float(size) if '.' in size else int(size)
                else:
                    numeric_key = size
                processed_results[numeric_key] = files
            except (ValueError, TypeError):
                processed_results[size] = files
        
        print(f"处理后的结果键: {list(processed_results.keys())}")
        
        if loaded_search_type == 'duration' and len(processed_results) == 2:
            print("✅ 加载功能正确处理时长搜索结果")
        else:
            print("❌ 加载功能处理时长搜索结果错误")
        
        # 测试4：测试边界情况
        print(f"\n=== 测试4: 边界情况测试 ===")
        
        # 测试空结果
        app.current_results = {}
        search_type, filename = app._detect_search_type_and_filename()
        print(f"空结果检测: 类型={search_type}, 文件名={filename}")
        
        # 测试混合文件类型
        mixed_results = {
            1024: ["/test/file1.jpg", "/test/video1.mp4"],
            2048: ["/test/file2.txt", "/test/file3.jpg"]
        }
        
        app.current_results = mixed_results
        search_type, filename = app._detect_search_type_and_filename()
        print(f"混合文件类型检测: 类型={search_type}, 文件名={filename}")
        
        # 测试大数值（文件大小）
        large_size_results = {
            1048576: ["/test/large1.jpg", "/test/large2.jpg"],  # 1MB
            2097152: ["/test/huge1.jpg", "/test/huge2.jpg"]     # 2MB
        }
        
        app.current_results = large_size_results
        search_type, filename = app._detect_search_type_and_filename()
        print(f"大文件大小检测: 类型={search_type}, 文件名={filename}")
        
        if search_type == "size":
            print("✅ 大文件大小正确识别为大小搜索")
        else:
            print("❌ 大文件大小识别错误")
        
        # 清理临时文件
        try:
            os.remove(temp_save_file)
        except:
            pass
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

def test_filename_generation():
    """测试文件名生成功能"""
    print("\n" + "=" * 50)
    print("测试文件名生成功能")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试各种场景的文件名生成
        test_cases = [
            # (结果数据, 期望的搜索类型, 期望的文件名)
            ({}, "size", "search_results.json"),  # 空结果
            ({1024: ["/test/file1.jpg", "/test/file2.jpg"]}, "size", "same_size_files.json"),  # 图片大小搜索
            ({30: ["/test/video1.mp4", "/test/video2.mp4"]}, "duration", "same_duration_files.json"),  # 视频时长搜索
            ({1048576: ["/test/large1.jpg", "/test/large2.jpg"]}, "size", "same_size_files.json"),  # 大文件大小搜索
            ({120.5: ["/test/movie1.avi", "/test/movie2.avi"]}, "duration", "same_duration_files.json"),  # 长视频时长搜索
        ]
        
        for i, (results, expected_type, expected_filename) in enumerate(test_cases, 1):
            app.current_results = results
            actual_type, actual_filename = app._detect_search_type_and_filename()
            
            print(f"测试用例 {i}:")
            print(f"  结果: {results}")
            print(f"  期望: 类型={expected_type}, 文件名={expected_filename}")
            print(f"  实际: 类型={actual_type}, 文件名={actual_filename}")
            
            if actual_type == expected_type and actual_filename == expected_filename:
                print(f"  ✅ 测试用例 {i} 通过")
            else:
                print(f"  ❌ 测试用例 {i} 失败")
        
        root.destroy()
        
    except Exception as e:
        print(f"文件名生成测试出错: {str(e)}")

if __name__ == "__main__":
    test_save_load_functionality()
    test_filename_generation()
    
    print("\n" + "=" * 50)
    print("🔧 保存加载功能测试完成")
    print("=" * 50)
    
    print("\n📊 新增功能:")
    print("✅ 智能搜索类型检测")
    print("   - 根据文件扩展名和键值范围判断搜索类型")
    print("   - 视频文件 + 小数值 = 时长搜索")
    print("   - 其他情况 = 大小搜索")
    
    print("\n✅ 智能文件名生成")
    print("   - 时长搜索: same_duration_files.json")
    print("   - 大小搜索: same_size_files.json")
    print("   - 空结果: search_results.json")
    
    print("\n✅ 保存数据增强")
    print("   - 添加 search_type 字段")
    print("   - 保持向后兼容性")
    print("   - 完整的元数据信息")
    
    print("\n✅ 加载功能增强")
    print("   - 识别搜索类型信息")
    print("   - 显示搜索类型提示")
    print("   - 正确处理数值键转换")
    
    print("\n💡 用户体验改进:")
    print("- 保存时自动选择合适的文件名")
    print("- 加载时显示搜索类型信息")
    print("- 区分不同类型的搜索结果")
    print("- 保持数据完整性和一致性")
