#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证刷新性能优化效果
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_optimized_refresh():
    """测试优化后的刷新性能"""
    print("测试优化后的刷新性能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="optimized_refresh_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建更多的测试文件来模拟真实场景
        all_files = []
        
        # 创建10个不同大小的组，每组3个文件
        for i in range(10):
            size_kb = (i + 1) * 2  # 2KB, 4KB, 6KB, ...
            for j in range(3):
                file_path = os.path.join(test_dir, f"file_{size_kb}kb_{j}.jpg")
                with open(file_path, 'w') as f:
                    f.write('A' * (size_kb * 1024))
                all_files.append(file_path)
        
        print(f"创建了 {len(all_files)} 个测试文件")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行按大小搜索
        print(f"\n执行按大小搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        # 测试1：删除单个文件（不导致组删除）
        print(f"\n=== 测试1: 删除单个文件（不导致组删除） ===")
        
        # 找一个有3个文件的组
        test_group_size = None
        test_files = None
        for size, files in app.current_results.items():
            if len(files) >= 3:
                test_group_size = size
                test_files = files
                break
        
        if test_group_size and test_files:
            file_to_delete = test_files[0]
            print(f"删除文件: {os.path.basename(file_to_delete)} (组还剩 {len(test_files)-1} 个文件)")
            
            start_time = time.time()
            app.delete_file(file_to_delete, test_group_size)
            refresh_time = time.time() - start_time
            
            print(f"删除后刷新耗时: {refresh_time:.3f} 秒")
            
            # 检查是否使用了增量更新
            if refresh_time < 0.02:  # 如果很快，可能使用了增量更新
                print("✓ 可能使用了增量更新")
            else:
                print("? 可能使用了完全更新")
        
        # 测试2：删除导致组删除的文件
        print(f"\n=== 测试2: 删除导致组删除的文件 ===")
        
        # 找一个只有2个文件的组
        test_group_size = None
        test_files = None
        for size, files in app.current_results.items():
            if len(files) == 2:
                test_group_size = size
                test_files = files
                break
        
        if test_group_size and test_files:
            file_to_delete = test_files[0]
            print(f"删除文件: {os.path.basename(file_to_delete)} (删除后组将被移除)")
            
            start_time = time.time()
            app.delete_file(file_to_delete, test_group_size)
            refresh_time = time.time() - start_time
            
            print(f"删除后刷新耗时: {refresh_time:.3f} 秒")
            
            # 检查组是否被正确删除
            if test_group_size not in app.current_results:
                print("✓ 组已被正确删除")
            else:
                print("✗ 组未被删除")
        
        # 测试3：批量删除多个文件
        print(f"\n=== 测试3: 批量删除多个文件 ===")
        
        # 选择多个文件进行批量删除
        files_to_delete = []
        for size, files in list(app.current_results.items())[:3]:  # 前3个组
            if files:
                files_to_delete.append(files[0])
        
        if files_to_delete:
            print(f"准备批量删除 {len(files_to_delete)} 个文件")
            
            # 模拟勾选文件
            for file_path in files_to_delete:
                if file_path not in app.checkbox_vars:
                    app.checkbox_vars[file_path] = tk.BooleanVar()
                app.checkbox_vars[file_path].set(True)
            
            start_time = time.time()
            app.delete_selected_files()
            batch_refresh_time = time.time() - start_time
            
            print(f"批量删除后刷新耗时: {batch_refresh_time:.3f} 秒")
        
        # 测试4：移除操作的性能
        print(f"\n=== 测试4: 移除操作的性能 ===")
        
        if app.current_results:
            # 找一个文件进行移除测试
            first_size = next(iter(app.current_results.keys()))
            first_files = app.current_results[first_size]
            if first_files:
                file_to_remove = first_files[0]
                print(f"移除文件: {os.path.basename(file_to_remove)}")
                
                start_time = time.time()
                app.remove_single_file(file_to_remove, first_size)
                remove_refresh_time = time.time() - start_time
                
                print(f"移除后刷新耗时: {remove_refresh_time:.3f} 秒")
        
        # 性能总结
        print(f"\n=== 性能总结 ===")
        print(f"当前剩余组数: {len(app.current_results)}")
        
        # 测试最终的显示性能
        start_time = time.time()
        app.display_results(app.current_results)
        final_display_time = time.time() - start_time
        print(f"完全重新显示耗时: {final_display_time:.3f} 秒")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_cache_performance():
    """测试缓存性能"""
    print("\n" + "=" * 50)
    print("测试缓存性能")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 创建大量测试数据
        large_results = {}
        for i in range(50):  # 50个组
            size = 1024 * (i + 1)
            files = [f"/test/file_{size}_{j}.jpg" for j in range(4)]  # 每组4个文件
            large_results[size] = files
        
        print(f"创建大量测试数据: {len(large_results)} 个组")
        
        # 测试完全更新的性能
        start_time = time.time()
        app.display_results(large_results)
        full_update_time = time.time() - start_time
        print(f"完全更新耗时: {full_update_time:.3f} 秒")
        
        # 模拟删除一些文件
        deleted_files = ["/test/file_1024_0.jpg", "/test/file_2048_0.jpg"]
        
        # 从结果中移除这些文件
        for file_path in deleted_files:
            for size, files in large_results.items():
                if file_path in files:
                    files.remove(file_path)
                    if len(files) < 2:
                        del large_results[size]
                    break
        
        # 测试缓存恢复的性能
        app.current_results = large_results
        
        start_time = time.time()
        success = app.load_from_cache_after_delete(deleted_files)
        cache_recovery_time = time.time() - start_time
        
        print(f"缓存恢复耗时: {cache_recovery_time:.3f} 秒")
        print(f"缓存恢复成功: {success}")
        
        if success and cache_recovery_time < full_update_time * 0.5:
            print("✓ 缓存恢复性能良好")
        else:
            print("? 缓存恢复性能可能需要优化")
        
        root.destroy()
        
    except Exception as e:
        print(f"缓存性能测试出错: {str(e)}")

if __name__ == "__main__":
    test_optimized_refresh()
    test_cache_performance()
    
    print("\n" + "=" * 50)
    print("🚀 刷新优化验证完成")
    print("=" * 50)
    
    print("\n📊 优化内容:")
    print("✅ 智能选择更新策略")
    print("   - 少量组删除: 尝试增量更新")
    print("   - 大量组删除: 使用完全更新")
    print("   - 增量更新失败: 自动降级到完全更新")
    
    print("\n✅ 避免不必要的完全更新")
    print("   - 移除了强制完全更新的逻辑")
    print("   - 优先使用缓存恢复机制")
    print("   - 只在必要时才进行完全重建")
    
    print("\n📈 预期性能提升:")
    print("- 单个文件删除: 更多使用增量更新")
    print("- 组删除操作: 智能选择更新策略")
    print("- 批量操作: 减少重复的完全更新")
    print("- 整体响应: 更快的界面刷新")
    
    print("\n💡 优化原理:")
    print("问题根源: 之前每次组删除都强制完全更新")
    print("解决方案: 智能判断是否需要完全更新")
    print("技术手段: 增强的缓存恢复 + 增量更新优先")
    print("用户体验: 删除/移除操作响应更快")
