#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除文件后组自动移除的修复
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_delete_group_removal():
    """测试删除文件后组自动移除"""
    print("测试删除文件后组自动移除的修复")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="delete_group_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        # 组1：2个文件（删除1个后应该删除组）
        group1_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"group1_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * 1024)
            group1_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (1KB)")
        
        # 组2：3个文件（删除1个后应该保留组）
        group2_files = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"group2_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('B' * 2048)
            group2_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (2KB)")
        
        # 组3：4个文件（删除2个后应该保留组）
        group3_files = []
        for i in range(4):
            file_path = os.path.join(test_dir, f"group3_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('C' * 4096)
            group3_files.append(file_path)
            print(f"创建文件: {os.path.basename(file_path)} (4KB)")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"\n执行按大小搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        initial_groups = len(app.current_results)
        print(f"初始组数: {initial_groups}")
        
        # 测试1：删除组1中的1个文件（应该删除整个组）
        print(f"\n=== 测试1: 删除组1中的1个文件（应该删除整个组） ===")
        
        if 1024 in app.current_results:
            file_to_delete = group1_files[0]
            print(f"删除文件: {os.path.basename(file_to_delete)}")
            print(f"删除前组1有 {len(app.current_results[1024])} 个文件")
            
            # 执行删除
            app.delete_file(file_to_delete, 1024)
            
            # 检查组是否被删除
            if 1024 not in app.current_results:
                print("✅ 组1已被正确删除")
            else:
                remaining = len(app.current_results[1024])
                print(f"❌ 组1仍存在，剩余 {remaining} 个文件（应该被删除）")
        
        # 测试2：删除组2中的1个文件（应该保留组）
        print(f"\n=== 测试2: 删除组2中的1个文件（应该保留组） ===")
        
        if 2048 in app.current_results:
            file_to_delete = group2_files[0]
            print(f"删除文件: {os.path.basename(file_to_delete)}")
            print(f"删除前组2有 {len(app.current_results[2048])} 个文件")
            
            # 执行删除
            app.delete_file(file_to_delete, 2048)
            
            # 检查组是否保留
            if 2048 in app.current_results:
                remaining = len(app.current_results[2048])
                print(f"✅ 组2正确保留，剩余 {remaining} 个文件")
            else:
                print(f"❌ 组2被错误删除")
        
        # 测试3：删除组2中的另一个文件（应该删除组）
        print(f"\n=== 测试3: 删除组2中的另一个文件（应该删除组） ===")
        
        if 2048 in app.current_results and len(app.current_results[2048]) == 2:
            file_to_delete = app.current_results[2048][0]
            print(f"删除文件: {os.path.basename(file_to_delete)}")
            print(f"删除前组2有 {len(app.current_results[2048])} 个文件")
            
            # 执行删除
            app.delete_file(file_to_delete, 2048)
            
            # 检查组是否被删除
            if 2048 not in app.current_results:
                print("✅ 组2已被正确删除")
            else:
                remaining = len(app.current_results[2048])
                print(f"❌ 组2仍存在，剩余 {remaining} 个文件（应该被删除）")
        
        # 测试4：删除组3中的2个文件（应该保留组）
        print(f"\n=== 测试4: 删除组3中的2个文件（应该保留组） ===")
        
        if 4096 in app.current_results:
            files_to_delete = group3_files[:2]
            print(f"删除前组3有 {len(app.current_results[4096])} 个文件")
            
            for file_to_delete in files_to_delete:
                print(f"删除文件: {os.path.basename(file_to_delete)}")
                app.delete_file(file_to_delete, 4096)
            
            # 检查组是否保留
            if 4096 in app.current_results:
                remaining = len(app.current_results[4096])
                print(f"✅ 组3正确保留，剩余 {remaining} 个文件")
            else:
                print(f"❌ 组3被错误删除")
        
        # 最终状态检查
        print(f"\n=== 最终状态检查 ===")
        final_groups = len(app.current_results)
        print(f"最终组数: {final_groups}")
        
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
            if len(files) == 1:
                print(f"    ❌ 发现只有1个文件的组！")
        
        # 检查界面显示
        print(f"\n=== 界面显示检查 ===")
        content = app.text_area.get("1.0", tk.END)
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        group_lines = [line for line in lines if "文件大小:" in line]
        
        print(f"界面显示的组数: {len(group_lines)}")
        print(f"数据中的组数: {final_groups}")
        
        if len(group_lines) == final_groups:
            print("✅ 界面显示与数据一致")
        else:
            print("❌ 界面显示与数据不一致")
        
        # 性能检查
        print(f"\n=== 性能检查 ===")
        start_time = time.time()
        app.display_results(app.current_results)
        display_time = time.time() - start_time
        print(f"完全重新显示耗时: {display_time:.3f}秒")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 测试1：删除不存在的文件
        print("测试1: 删除不存在的文件")
        app.current_results = {1024: ["/test/file1.jpg", "/test/file2.jpg"]}
        
        try:
            app.delete_file("/test/nonexistent.jpg", 1024)
            print("✅ 删除不存在文件处理正常")
        except Exception as e:
            print(f"❌ 删除不存在文件出错: {str(e)}")
        
        # 测试2：删除不存在组中的文件
        print("\n测试2: 删除不存在组中的文件")
        try:
            app.delete_file("/test/file1.jpg", 9999)
            print("✅ 删除不存在组中文件处理正常")
        except Exception as e:
            print(f"❌ 删除不存在组中文件出错: {str(e)}")
        
        root.destroy()
        
    except Exception as e:
        print(f"边界测试出错: {str(e)}")

if __name__ == "__main__":
    test_delete_group_removal()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("🔧 删除组自动移除测试完成")
    print("=" * 50)
    
    print("\n📊 修复内容:")
    print("✅ 修复了删除文件后组不自动移除的问题")
    print("✅ 在delete_file中记录被删除的组信息")
    print("✅ 将被删除组信息传递给增量更新方法")
    print("✅ 增量更新能正确处理组删除")
    
    print("\n🎯 修复原理:")
    print("问题: 增量更新检查单文件组时，组已经被删除了")
    print("解决: 在删除组之前记录组信息，传递给增量更新")
    print("结果: 增量更新能正确识别需要移除的组")
    
    print("\n💡 预期效果:")
    print("- 删除文件后只剩1个文件的组会被立即删除")
    print("- 界面显示会立即反映数据变化")
    print("- 不会再出现只有1个文件的'重复'组")
    print("- 保持删除操作的高性能")
