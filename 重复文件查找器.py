import os
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from collections import defaultdict
import threading
from PIL import Image, ImageTk
import cv2
import mimetypes
from pathlib import Path
import json
import datetime
import platform
import ctypes
from ctypes import windll
import win32net
import time

class CustomDialog(tk.Toplevel):
    def __init__(self, parent, title, message, dialog_type='info'):
        super().__init__(parent)
        
        # 设置对话框属性
        self.title(title)
        self.resizable(False, False)
        self.transient(parent)  # 设置为父窗口的临时窗口
        
        # 确保对话框显示在父窗口之上
        self.grab_set()
        
        # 创建对话框内容
        frame = ttk.Frame(self, padding="20")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 根据类型选择图标
        icon_name = {
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️',
            'yesno': '❓'
        }.get(dialog_type, 'ℹ️')
        
        # 显示图标和消息
        icon_label = ttk.Label(frame, text=icon_name, font=('TkDefaultFont', 24))
        icon_label.grid(row=0, column=0, padx=(0, 10), rowspan=2)
        
        # 处理消息中的特殊字符
        message = message.replace('\\', '/')
        
        # 如果消息中包含文件路径，分开显示
        if '确定要删除文件' in message:
            parts = message.split('\n')
            message_text = parts[0]
            file_path = parts[1] if len(parts) > 1 else ""
            
            message_label = ttk.Label(frame, text=message_text)
            message_label.grid(row=0, column=1, columnspan=2, sticky=tk.W)
            
            # 创建文本框显示文件路径
            path_frame = ttk.Frame(frame)
            path_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
            
            path_text = tk.Text(path_frame, wrap=tk.WORD, height=3, width=50)
            path_text.insert('1.0', file_path)
            path_text.configure(state='disabled')
            path_text.pack(fill=tk.BOTH, expand=True)
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(path_frame, orient="vertical", command=path_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            path_text.configure(yscrollcommand=scrollbar.set)
        else:
            message_label = ttk.Label(frame, text=message, wraplength=400)
            message_label.grid(row=0, column=1, columnspan=2, sticky=tk.W)
        
        # 按钮区域
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=(20, 0))
        
        self.result = False
        
        if dialog_type == 'yesno':
            # 确认和取消按钮
            yes_btn = ttk.Button(button_frame, text="确定", command=self.on_yes, style='Accent.TButton')
            yes_btn.pack(side=tk.LEFT, padx=5)
            
            no_btn = ttk.Button(button_frame, text="取消", command=self.on_no)
            no_btn.pack(side=tk.LEFT, padx=5)
            
            # 设置默认焦点到"取消"按钮
            self.after(100, lambda: no_btn.focus_set())
        else:
            # 确定按钮
            ok_btn = ttk.Button(button_frame, text="确定", command=self.on_yes)
            ok_btn.pack(side=tk.LEFT, padx=5)
            self.after(100, lambda: ok_btn.focus_set())
        
        # 绑定回车键和ESC键
        self.bind('<Return>', lambda e: self.on_yes())
        self.bind('<Escape>', lambda e: self.on_no())
        
        # 居中显示
        self.center_window()
        
    def center_window(self):
        """将窗口居中显示"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')
        
    def on_yes(self):
        self.result = True
        self.destroy()
        
    def on_no(self):
        self.result = False
        self.destroy()

class FileSearchApp:
    def __init__(self, root):
        self.root = root
        self.root.title("重复文件查找器")
        self.root.geometry("1400x768")

        # 添加最小窗口大小限制
        self.root.minsize(1200, 600)
        
        # 添加勾选框变量字典
        self.checkbox_vars = {}
        
        # Windows系统下的特殊处理
        if platform.system() == 'Windows':
            try:
                # 设置任务栏图标
                self.root.iconbitmap(default="")
                # 设置窗口样式
                GWL_EXSTYLE = -20
                WS_EX_APPWINDOW = 0x00040000
                WS_EX_TOOLWINDOW = 0x00000080
                hwnd = self.root.winfo_id()
                style = windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                style = style & ~WS_EX_TOOLWINDOW
                style = style | WS_EX_APPWINDOW
                windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, style)
                # 将窗口设置为前台
                self.root.lift()
                self.root.attributes('-topmost', True)
                self.root.attributes('-topmost', False)
            except Exception as e:
                print(f"Windows特殊处理出错: {str(e)}")
        
        # 添加资源管理
        self._preview_cache = {}  # 缓存预览图片
        self._current_preview = None  # 当前预览的文件路径
        self._last_preview = None  # 上一个预览的文件路径

        # 添加性能优化标志
        self._is_processing = False  # 防止重复处理
        self._preview_timer = None  # 用于延迟加载预览

        # 添加文件信息缓存
        self._file_info_cache = {}  # 缓存文件信息（大小、时长等）
        self._scan_results_cache = {}  # 缓存扫描结果
        self._last_scan_directory = ""  # 上次扫描的目录
        self._last_scan_timestamp = 0  # 上次扫描的时间戳

        # 添加显示信息缓存
        self._display_info_cache = {}  # 缓存文件的显示信息（相对路径、格式化大小等）
        self._ui_components_cache = {}  # 缓存UI组件信息
        
        # 配置文件路径
        self.config_file = os.path.join(os.path.expanduser("~"), ".same_size_files_config.json")
        # 修改删除记录文件路径
        self.delete_log_file = r"z:\work\same_size_files_delete_index.txt"
        
        # 加载上次的配置
        self.last_directory = ""
        self.last_loaded_file = ""
        self.load_config()
        
        # 如果是Windows系统，确保能访问网络驱动器
        if platform.system() == 'Windows':
            self.init_network_drives()
        
        # 创建主框架并使用pack布局替代grid
        self.main_frame = ttk.Frame(root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建按钮框架
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.pack(fill=tk.X, pady=5)

        # 创建第一行按钮框架
        self.button_row1 = ttk.Frame(self.button_frame)
        self.button_row1.pack(fill=tk.X, pady=2)

        # 第一行：文件夹操作和搜索按钮
        self.select_btn = ttk.Button(self.button_row1, text="选择文件夹", command=self.select_directory)
        self.select_btn.pack(side=tk.LEFT, padx=5)

        self.search_by_size_btn = ttk.Button(self.button_row1, text="相同大小", command=self.search_by_size, state='disabled')
        self.search_by_size_btn.pack(side=tk.LEFT, padx=5)

        self.search_by_duration_btn = ttk.Button(self.button_row1, text="相同时长", command=self.search_by_duration, state='disabled')
        self.search_by_duration_btn.pack(side=tk.LEFT, padx=5)

        self.save_btn = ttk.Button(self.button_row1, text="保存结果", command=self.save_results, state='disabled')
        self.save_btn.pack(side=tk.LEFT, padx=5)

        self.load_btn = ttk.Button(self.button_row1, text="加载结果", command=self.load_results)
        self.load_btn.pack(side=tk.LEFT, padx=5)

        # 添加包含图片文件的勾选框
        self.include_images_var = tk.BooleanVar(value=False)
        self.include_images_checkbox = ttk.Checkbutton(self.button_row1, text="包含图片文件",
                                                      variable=self.include_images_var)
        self.include_images_checkbox.pack(side=tk.LEFT, padx=5)

        # 创建第二行按钮框架
        self.button_row2 = ttk.Frame(self.button_frame)
        self.button_row2.pack(fill=tk.X, pady=2)

        # 第二行：文件操作按钮和选择选项
        self.select_all_btn = ttk.Button(self.button_row2, text="全选", command=self.select_all_files)
        self.select_all_btn.pack(side=tk.LEFT, padx=5)

        self.deselect_all_btn = ttk.Button(self.button_row2, text="取消全选", command=self.deselect_all_files)
        self.deselect_all_btn.pack(side=tk.LEFT, padx=5)

        self.delete_selected_btn = ttk.Button(self.button_row2, text="删除选中", command=self.delete_selected_files)
        self.delete_selected_btn.pack(side=tk.LEFT, padx=5)

        self.remove_from_list_btn = ttk.Button(self.button_row2, text="移出列表", command=self.remove_selected_from_list)
        self.remove_from_list_btn.pack(side=tk.LEFT, padx=5)

        # 选择选项勾选框
        self.same_dir_var = tk.BooleanVar()
        self.same_dir_checkbox = ttk.Checkbutton(self.button_row2, text="同目录选择",
                                                variable=self.same_dir_var)
        self.same_dir_checkbox.pack(side=tk.LEFT, padx=5)

        self.same_time_var = tk.BooleanVar()
        self.same_time_checkbox = ttk.Checkbutton(self.button_row2, text="相同时段",
                                                 variable=self.same_time_var)
        self.same_time_checkbox.pack(side=tk.LEFT, padx=5)
        
        # 当前文件夹路径显示
        self.path_var = tk.StringVar()
        self.path_label = ttk.Label(self.main_frame, textvariable=self.path_var)
        self.path_label.pack(fill=tk.X, pady=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(self.main_frame, length=300, mode='determinate', variable=self.progress_var)
        self.progress.pack(fill=tk.X, pady=5)
        
        # 当前处理文件显示
        self.current_file_var = tk.StringVar()
        self.current_file_label = ttk.Label(self.main_frame, textvariable=self.current_file_var)
        self.current_file_label.pack(fill=tk.X, pady=5)
        
        # 创建水平分隔框架
        self.paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧结果显示区域
        self.result_frame = ttk.LabelFrame(self.paned_window, text="搜索结果")
        self.paned_window.add(self.result_frame, weight=1)
        
        # 创建文本框和滚动条的容器
        self.text_container = ttk.Frame(self.result_frame)
        self.text_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建文本框和滚动条，使用等宽字体以便对齐
        self.text_area = tk.Text(self.text_container, wrap=tk.WORD, font=('Consolas', 9))
        self.scrollbar = ttk.Scrollbar(self.text_container, orient="vertical", command=self.text_area.yview)
        self.text_area.configure(yscrollcommand=self.scrollbar.set)

        # 设置制表符位置，确保按钮对齐
        self.text_area.configure(tabs=('500p', '580p'))  # 设置两个制表符位置
        
        self.text_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧预览区域
        self.preview_frame = ttk.LabelFrame(self.paned_window, text="预览")
        self.paned_window.add(self.preview_frame, weight=1)

        # 创建预览控制按钮框架
        self.preview_control_frame = ttk.Frame(self.preview_frame)
        self.preview_control_frame.pack(fill=tk.X, padx=5, pady=(5, 0))

        # 添加停止预览按钮
        self.stop_preview_btn = ttk.Button(self.preview_control_frame, text="停止预览",
                                          command=self.stop_preview, state='disabled')
        self.stop_preview_btn.pack(side=tk.LEFT, padx=5)

        # 添加当前预览文件名显示
        self.preview_filename_var = tk.StringVar()
        self.preview_filename_label = ttk.Label(self.preview_control_frame,
                                               textvariable=self.preview_filename_var,
                                               font=('微软雅黑', 9))
        self.preview_filename_label.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)

        # 创建预览标签的容器
        self.preview_container = ttk.Frame(self.preview_frame)
        self.preview_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建预览标签
        self.preview_label = ttk.Label(self.preview_container, text="选择文件进行预览")
        self.preview_label.pack(expand=True, fill=tk.BOTH)
        
        # 添加选中文件显示区域
        self.selected_files_frame = ttk.LabelFrame(self.main_frame, text="已选中文件 (0 个文件，0 字节)")
        self.selected_files_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建选中文件的树形视图
        self.selected_tree = ttk.Treeview(self.selected_files_frame, selectmode='browse')
        self.selected_tree.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        # 为树形视图添加滚动条
        self.selected_tree_scroll = ttk.Scrollbar(self.selected_files_frame, 
                                                orient="vertical", 
                                                command=self.selected_tree.yview)
        self.selected_tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.selected_tree.configure(yscrollcommand=self.selected_tree_scroll.set)
        
        # 配置树形视图的列
        self.selected_tree["columns"] = ("size",)
        self.selected_tree.column("#0", width=400, stretch=tk.YES)
        self.selected_tree.column("size", width=100, anchor=tk.E)
        self.selected_tree.heading("#0", text="文件路径")
        self.selected_tree.heading("size", text="大小")

        # 绑定树形视图的点击事件
        self.selected_tree.bind("<Button-1>", self.on_selected_tree_click)
        self.selected_tree.bind("<Double-1>", self.on_selected_tree_click)
        self.selected_tree.bind("<<TreeviewSelect>>", self.on_selected_tree_select)
        
        # 创建底部状态栏
        self.status_frame = ttk.Frame(root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)
        
        # 添加分隔线
        self.separator = ttk.Separator(root, orient='horizontal')
        self.separator.pack(fill=tk.X, side=tk.BOTTOM, padx=5)
        
        # 左侧显示操作状态
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 右侧显示文件统计信息
        self.stats_label = ttk.Label(self.status_frame, text="已选择: 0 个文件，总大小: 0 字节")
        self.stats_label.pack(side=tk.RIGHT, padx=5)
        
        # 配置样式
        self.style = ttk.Style()
        self.style.configure("Stats.TLabel", font=("微软雅黑", 9))
        self.stats_label.configure(style="Stats.TLabel")
        
        # 配置grid权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(4, weight=1)
        self.result_frame.columnconfigure(0, weight=1)
        self.result_frame.rowconfigure(0, weight=1)
        self.preview_frame.columnconfigure(0, weight=1)
        self.preview_frame.rowconfigure(0, weight=1)
        
        # 绑定文本区域点击事件
        self.text_area.tag_configure("file", foreground="blue", underline=1)
        self.text_area.tag_bind("file", "<Button-1>", self.on_file_click)
        self.text_area.tag_bind("file", "<Enter>", self.on_file_enter)
        self.text_area.tag_bind("file", "<Leave>", self.on_file_leave)
        
        # 视频相关变量
        self.video_capture = None
        self.is_playing = False
        self.video_fps = 30  # 默认帧率
        self.frame_interval = 33  # 默认间隔（毫秒）
        self.frame_skip = 1  # 跳帧数，1表示不跳帧
        self.last_frame_time = 0  # 上一帧的时间戳
        
        # 存储原始鼠标指针
        self.original_cursor = self.text_area.cget("cursor")
        self.current_directory = ""
        self.current_results = {}
        
        # 在__init__方法中添加删除按钮的样式
        self.style = ttk.Style()
        self.style.configure("Delete.TButton", padding=2)
        self.style.configure("Highlight.TLabel", background="#e0e0ff")
        self.style.configure("Selected.TLabel", background="#c0c0ff")
        
        # 存储删除按钮的引用
        self.delete_buttons = {}
        # 存储当前选中的文件标签
        self.selected_label = None

        # 工具提示相关
        self.tooltip = None
        self.tooltip_text = ""
        
        # 添加样式
        self.style.configure('Accent.TButton', foreground='black', background='#0078D4')

    def on_file_click(self, event):
        # 获取点击位置的文件路径
        index = self.text_area.index(f"@{event.x},{event.y}")
        tags = self.text_area.tag_names(index)
        if "file" in tags:
            # 获取文件路径
            file_path = self.text_area.get(f"{index} linestart", f"{index} lineend").strip("  - ")
            self.preview_file(file_path)

    def on_file_enter(self, event):
        # 当鼠标进入可预览文件区域时，改变鼠标指针为手型
        self.text_area.configure(cursor="hand2")

    def on_file_leave(self, event):
        # 当鼠标离开可预览文件区域时，恢复原始鼠标指针
        self.text_area.configure(cursor=self.original_cursor)

    def preview_file(self, file_path):
        # 如果正在处理其他预览，或者是相同文件，则跳过
        if self._is_processing or file_path == self._current_preview:
            return
            
        # 首先检查文件是否存在
        if not os.path.exists(file_path):
            # 查找文件所在的大小组
            size = None
            for s, files in self.current_results.items():
                if file_path in files:
                    size = s
                    break
                    
            if size is not None:
                # 执行和删除文件相同的数据更新流程
                # 清理预览缓存
                if file_path in self._preview_cache:
                    del self._preview_cache[file_path]
                if file_path == self._current_preview:
                    self._current_preview = None
                
                # 更新界面和内存中的数据
                self.current_results[size].remove(file_path)
                if not self.current_results[size]:
                    del self.current_results[size]
                self.display_results(self.current_results)
                self.preview_label.configure(image='', text="文件不存在，已从列表中移除")
                
                # 更新保存按钮状态
                if self.current_results:
                    self.save_btn.configure(state='normal')
                else:
                    self.save_btn.configure(state='disabled')
                    self.text_area.delete(1.0, tk.END)
                    self.text_area.insert(tk.END, "未找到大小相同的文件\n")
                
                # 如果有已加载的结果文件，自动更新它
                if self.last_loaded_file and os.path.exists(self.last_loaded_file):
                    try:
                        # 准备保存的数据
                        save_data = {
                            "directory": self.current_directory,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "results": self.current_results
                        }
                        
                        # 保存到原文件
                        with open(self.last_loaded_file, 'w', encoding='utf-8') as f:
                            json.dump(save_data, f, ensure_ascii=False, indent=2)
                        print(f"结果文件已更新: {self.last_loaded_file}")
                    except Exception as e:
                        print(f"更新结果文件失败: {str(e)}")
                        messagebox.showwarning("警告", f"结果文件自动更新失败，建议手动保存。\n错误信息：{str(e)}")
            else:
                self.preview_label.configure(image='', text="文件不存在")
            return
            
        # 获取文件类型
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            return
            
        # 停止当前视频播放
        self.stop_video_playback()

        # 启用停止按钮并显示文件名
        self.stop_preview_btn.configure(state='normal')
        filename = os.path.basename(file_path)
        self.preview_filename_var.set(filename)

        # 如果预览已在缓存中，直接使用
        if file_path in self._preview_cache:
            self.preview_label.configure(image=self._preview_cache[file_path])
            self._current_preview = file_path
            return
            
        # 设置处理标志
        self._is_processing = True
        
        # 取消之前的定时器
        if self._preview_timer:
            self.root.after_cancel(self._preview_timer)
        
        # 创建新的预览任务
        def load_preview():
            try:
                # 再次检查文件是否存在（可能在延迟期间被删除）
                if not os.path.exists(file_path):
                    self.preview_label.configure(image='', text="文件不存在，可能已被删除")
                    return
                    
                if mime_type.startswith('image/'):
                    self.show_image(file_path)
                elif mime_type.startswith('video/'):
                    self.show_video(file_path)
                    
                # 更新当前预览路径
                self._current_preview = file_path
                
                # 清理旧的缓存
                if len(self._preview_cache) > 10:  # 最多保留10个预览
                    oldest = next(iter(self._preview_cache))
                    del self._preview_cache[oldest]
            finally:
                self._is_processing = False
                
        # 延迟100ms加载预览，避免快速切换时的资源浪费
        self._preview_timer = self.root.after(100, load_preview)

    def show_image(self, image_path):
        try:
            # 使用PIL的lazy loading
            with Image.open(image_path) as img:
                # 获取预览区域的大小
                preview_width = self.preview_frame.winfo_width() - 20
                preview_height = self.preview_frame.winfo_height() - 20
                
                # 计算缩放比例
                width, height = img.size
                ratio = min(preview_width/width, preview_height/height)
                new_size = (int(width*ratio), int(height*ratio))
                
                # 只在必要时调整大小
                if ratio < 1:
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # 转换为PhotoImage并缓存
                photo = ImageTk.PhotoImage(img)
                self._preview_cache[image_path] = photo
                self.preview_label.configure(image=photo)

                # 图片预览完成后禁用停止按钮（图片不需要持续播放）
                self.stop_preview_btn.configure(state='disabled')

        except Exception as e:
            self.preview_label.configure(image='', text=f"无法预览图片: {str(e)}")
            # 出错时也禁用停止按钮
            self.stop_preview_btn.configure(state='disabled')

    def show_video(self, video_path):
        if not self.is_playing:
            try:
                # 延迟初始化视频捕获
                def init_video():
                    self.video_capture = cv2.VideoCapture(video_path)
                    if not self.video_capture.isOpened():
                        raise Exception("无法打开视频文件")

                    # 获取视频的实际帧率
                    fps = self.video_capture.get(cv2.CAP_PROP_FPS)
                    if fps > 0 and fps <= 120:  # 合理的帧率范围
                        self.video_fps = fps

                        # 简化策略：直接设置为合理的播放速度
                        # 对于预览来说，25-30fps已经足够流畅
                        if fps > 60:
                            self.frame_skip = int(fps / 25)  # 降到25fps
                            self.frame_interval = 40  # 25fps
                        elif fps > 30:
                            self.frame_skip = 2  # 降到一半帧率
                            self.frame_interval = int(2000 / fps)
                        else:
                            self.frame_skip = 1  # 保持原帧率
                            self.frame_interval = max(int(1000 / fps), 25)  # 最快40fps
                    else:
                        # 如果获取不到有效帧率，使用默认值
                        self.video_fps = 25
                        self.frame_interval = 40  # 25fps
                        self.frame_skip = 2

                    #print(f"视频帧率: {self.video_fps:.2f} fps, 跳帧: {self.frame_skip}, 目标播放间隔: {self.frame_interval} ms")

                    self.is_playing = True
                    self.last_frame_time = 0
                    self.update_video_frame()

                # 在新线程中初始化视频
                threading.Thread(target=init_video, daemon=True).start()

            except Exception as e:
                self.preview_label.configure(text=f"无法预览视频: {str(e)}")
                self.is_playing = False
                # 出错时禁用停止按钮
                self.stop_preview_btn.configure(state='disabled')

    def update_video_frame(self):
        if not self.is_playing or self.video_capture is None:
            return
            
        try:
            # 跳帧处理：读取多帧但只显示最后一帧
            frame = None
            ret = False
            for i in range(self.frame_skip):
                ret, frame = self.video_capture.read()
                if not ret:
                    break

            if ret:
                # 获取预览区域的大小
                preview_width = self.preview_frame.winfo_width() - 20
                preview_height = self.preview_frame.winfo_height() - 20

                # 如果预览区域太小，跳过这一帧
                if preview_width < 100 or preview_height < 100:
                    if self.is_playing:
                        self.root.after(self.frame_interval, self.update_video_frame)
                    return

                # 限制最大预览尺寸，避免处理过大的图像
                max_preview_size = 400
                if preview_width > max_preview_size:
                    preview_width = max_preview_size
                if preview_height > max_preview_size:
                    preview_height = max_preview_size

                # 计算缩放比例
                height, width = frame.shape[:2]
                ratio = min(preview_width/width, preview_height/height)

                # 如果需要大幅缩放，先进行快速缩放
                if ratio < 0.5:
                    # 先快速缩放到中等大小
                    temp_size = (int(width * 0.5), int(height * 0.5))
                    frame = cv2.resize(frame, temp_size, interpolation=cv2.INTER_AREA)
                    height, width = frame.shape[:2]
                    ratio = min(preview_width/width, preview_height/height)

                # 最终缩放
                if ratio < 1.0:
                    new_size = (int(width*ratio), int(height*ratio))
                    frame = cv2.resize(frame, new_size, interpolation=cv2.INTER_LINEAR)

                # 直接转换为PhotoImage，跳过PIL Image步骤
                # 转换颜色空间从BGR到RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 使用更高效的方式创建PhotoImage
                photo = ImageTk.PhotoImage(Image.fromarray(frame_rgb))

                self.preview_label.configure(image=photo)
                self.preview_label.image = photo

                # 使用动态计算的帧间隔
                if self.is_playing:
                    self.root.after(self.frame_interval, self.update_video_frame)
            else:
                # 视频播放完毕，重新开始
                self.video_capture.set(cv2.CAP_PROP_POS_FRAMES, 0)
                if self.is_playing:
                    self.root.after(self.frame_interval, self.update_video_frame)
        except Exception as e:
            print(f"视频播放出错: {e}")
            self.is_playing = False
            # 出错时禁用停止按钮
            self.stop_preview_btn.configure(state='disabled')

    def stop_video_playback(self):
        self.is_playing = False
        if self.video_capture is not None:
            self.video_capture.release()
            self.video_capture = None

    def stop_preview(self):
        """停止当前预览"""
        try:
            # 停止视频播放
            self.stop_video_playback()

            # 清除预览内容
            self.preview_label.configure(image='', text="预览已停止")
            self.preview_label.image = None

            # 清除当前预览文件信息
            self._current_preview = None
            self.preview_filename_var.set("")

            # 禁用停止按钮
            self.stop_preview_btn.configure(state='disabled')

            # 取消预览定时器
            if self._preview_timer:
                self.root.after_cancel(self._preview_timer)
                self._preview_timer = None

            # 重置处理标志
            self._is_processing = False

            self.show_status("预览已停止")

        except Exception as e:
            print(f"停止预览时出错: {str(e)}")
            self.show_status(f"停止预览失败: {str(e)}")
            
    def __del__(self):
        # 清理资源
        self.stop_video_playback()
        self._preview_cache.clear()
        self._file_info_cache.clear()
        self._scan_results_cache.clear()

    def get_cached_file_info(self, file_path):
        """获取缓存的文件信息"""
        if file_path in self._file_info_cache:
            return self._file_info_cache[file_path]
        return None

    def cache_file_info(self, file_path, info):
        """缓存文件信息"""
        self._file_info_cache[file_path] = info

    def invalidate_file_cache(self, file_path):
        """使指定文件的缓存失效"""
        if file_path in self._file_info_cache:
            del self._file_info_cache[file_path]
        if file_path in self._preview_cache:
            del self._preview_cache[file_path]

    def smart_cache_cleanup(self):
        """智能清理缓存 - 只清理不存在文件的缓存"""
        try:
            # 清理文件信息缓存中不存在的文件
            info_keys_to_remove = []
            for path in self._file_info_cache.keys():
                if not os.path.exists(path):
                    info_keys_to_remove.append(path)

            for path in info_keys_to_remove:
                del self._file_info_cache[path]

            # 清理预览缓存中不存在的文件
            preview_keys_to_remove = []
            for path in self._preview_cache.keys():
                if not os.path.exists(path):
                    preview_keys_to_remove.append(path)

            for path in preview_keys_to_remove:
                del self._preview_cache[path]

            if info_keys_to_remove or preview_keys_to_remove:
                print(f"智能缓存清理: 移除了 {len(info_keys_to_remove)} 个文件信息缓存, {len(preview_keys_to_remove)} 个预览缓存")

        except Exception as e:
            print(f"智能缓存清理失败: {str(e)}")

    def load_from_cache_after_delete(self, deleted_files):
        """删除文件后从缓存调入视频列表信息"""
        try:
            print(f"开始从缓存恢复数据，删除了 {len(deleted_files)} 个文件")

            # 批量清理已删除文件的缓存
            for file_path in deleted_files:
                self.invalidate_file_cache(file_path)

            # 智能清理其他无效缓存
            self.smart_cache_cleanup()

            # 检查是否有组需要完全移除（只剩1个文件的组）
            groups_to_remove = set()
            for size, files in self.current_results.items():
                if len(files) < 2:
                    groups_to_remove.add(size)

            # 优化：如果只是少量组需要移除，尝试使用增量更新
            if groups_to_remove:
                total_groups = len(self.current_results) + len(groups_to_remove)

                # 如果需要移除的组数量较少（<30%），尝试增量更新
                if len(groups_to_remove) < max(3, total_groups * 0.3):
                    print(f"检测到 {len(groups_to_remove)} 个组需要移除，尝试增量更新")

                    # 先移除单文件组
                    for size in groups_to_remove:
                        if size in self.current_results:
                            del self.current_results[size]

                    # 尝试增量更新
                    if self.incremental_update_display(deleted_files):
                        # 更新保存按钮状态
                        if self.current_results:
                            self.save_btn.configure(state='normal')
                        else:
                            self.save_btn.configure(state='disabled')

                        self.show_status(f"已从缓存恢复，删除了 {len(deleted_files)} 个文件")
                        return True

                # 如果增量更新失败或组数量太多，使用完全更新
                print(f"检测到 {len(groups_to_remove)} 个组需要完全移除，使用完全更新")
                self.display_results(self.current_results)

                # 更新保存按钮状态
                if self.current_results:
                    self.save_btn.configure(state='normal')
                else:
                    self.save_btn.configure(state='disabled')

                self.show_status(f"已从缓存恢复，删除了 {len(deleted_files)} 个文件")
                return True

            # 如果有当前结果，尝试使用增量更新（放宽缓存条件）
            elif self.current_results:
                print("尝试使用增量更新快速恢复界面")
                # 使用增量更新而不是完全重新渲染
                if self.incremental_update_display(deleted_files):
                    # 更新保存按钮状态
                    if self.current_results:
                        self.save_btn.configure(state='normal')
                    else:
                        self.save_btn.configure(state='disabled')

                    self.show_status(f"已从缓存恢复，删除了 {len(deleted_files)} 个文件")
                    return True
                else:
                    # 增量更新失败，回退到完全更新
                    print("增量更新失败，回退到完全更新")
                    self.display_results(self.current_results)

                    # 更新保存按钮状态
                    if self.current_results:
                        self.save_btn.configure(state='normal')
                    else:
                        self.save_btn.configure(state='disabled')

                    self.show_status(f"已从缓存恢复，删除了 {len(deleted_files)} 个文件")
                    return True
            else:
                print("无当前结果，无法恢复")
                return False

        except Exception as e:
            print(f"从缓存恢复失败: {str(e)}")
            return False

    def incremental_update_display(self, deleted_files):
        """增量更新显示 - 只移除已删除的文件，不重新渲染整个界面"""
        try:
            print(f"开始增量更新，移除 {len(deleted_files)} 个文件")

            # 检查哪些组会因为删除文件而变成单文件组（需要完全移除）
            groups_to_remove_completely = set()
            for size, files in self.current_results.items():
                if len(files) < 2:  # 只剩1个或0个文件的组
                    groups_to_remove_completely.add(size)

            print(f"需要完全移除的组: {len(groups_to_remove_completely)} 个")

            # 如果有组需要完全移除，使用完全更新
            if groups_to_remove_completely:
                print("检测到需要完全移除的组，使用完全更新以确保正确性")
                self.display_results(self.current_results)
                return True

            # 记录需要移除的UI组件
            components_to_remove = []

            # 遍历已删除的文件，找到对应的UI组件
            for deleted_file in deleted_files:
                # 移除勾选框变量
                if deleted_file in self.checkbox_vars:
                    del self.checkbox_vars[deleted_file]

                # 移除删除按钮
                if deleted_file in self.delete_buttons:
                    self.delete_buttons[deleted_file].destroy()
                    del self.delete_buttons[deleted_file]

                # 查找并标记要删除的文本行
                tag_name = f"file_{deleted_file}"
                try:
                    # 获取标签的位置范围
                    ranges = self.text_area.tag_ranges(tag_name)
                    if ranges:
                        # 找到整行的范围
                        start_line = self.text_area.index(f"{ranges[0]} linestart")
                        end_line = self.text_area.index(f"{ranges[0]} lineend + 1c")
                        components_to_remove.append((start_line, end_line, tag_name))
                except tk.TclError:
                    # 标签不存在，跳过
                    pass

            # 按行号倒序排列，从后往前删除，避免位置偏移
            components_to_remove.sort(key=lambda x: x[0], reverse=True)

            # 删除UI组件和文本行
            for start_line, end_line, tag_name in components_to_remove:
                try:
                    # 删除文本行
                    self.text_area.delete(start_line, end_line)
                    # 移除标签
                    self.text_area.tag_delete(tag_name)
                except tk.TclError:
                    # 删除失败，跳过
                    pass

            # 检查是否有空的组需要移除分隔符
            self.cleanup_empty_groups()

            print(f"增量更新完成，移除了 {len(components_to_remove)} 个UI组件")
            return True

        except Exception as e:
            print(f"增量更新失败: {str(e)}")
            return False

    def cleanup_single_file_groups(self, results):
        """清理只有1个文件的组（额外保障机制）"""
        try:
            groups_to_remove = []
            for size, files in results.items():
                if len(files) < 2:
                    groups_to_remove.append(size)
                    print(f"发现单文件组: 大小{size}, 文件数{len(files)}, 将被清理")

            for size in groups_to_remove:
                del results[size]
                print(f"✓ 已清理单文件组: 大小{size}")

            if groups_to_remove:
                print(f"清理了 {len(groups_to_remove)} 个单文件组")

        except Exception as e:
            print(f"清理单文件组失败: {str(e)}")

    def cleanup_empty_groups(self):
        """清理空的文件组和多余的分隔符"""
        try:
            # 获取文本内容
            content = self.text_area.get("1.0", tk.END)
            lines = content.split('\n')

            # 查找连续的分隔符行并移除多余的
            separator = "=" * 50
            new_lines = []
            prev_was_separator = False

            for line in lines:
                if line.strip() == separator:
                    if not prev_was_separator:
                        new_lines.append(line)
                        prev_was_separator = True
                else:
                    new_lines.append(line)
                    prev_was_separator = False

            # 移除开头和结尾的分隔符
            while new_lines and new_lines[0].strip() == separator:
                new_lines.pop(0)
            while new_lines and new_lines[-1].strip() == separator:
                new_lines.pop()

            # 如果内容发生变化，更新文本区域
            new_content = '\n'.join(new_lines)
            if new_content != content:
                # 保存当前滚动位置
                current_view = self.text_area.yview()

                # 更新内容
                self.text_area.delete("1.0", tk.END)
                self.text_area.insert("1.0", new_content)

                # 恢复滚动位置
                self.text_area.yview_moveto(current_view[0])

        except Exception as e:
            print(f"清理空组失败: {str(e)}")

    def show_tooltip(self, event, text):
        """显示工具提示"""
        if self.tooltip:
            self.tooltip.destroy()

        self.tooltip = tk.Toplevel(self.root)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{event.x_root + 10}+{event.y_root + 10}")

        label = tk.Label(self.tooltip, text=text, background="lightyellow",
                        relief="solid", borderwidth=1, font=("微软雅黑", 9))
        label.pack()

    def hide_tooltip(self):
        """隐藏工具提示"""
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

    def show_file_context_menu(self, event, file_path, size_key):
        """显示文件右键菜单"""
        try:
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)

            # 添加复制文件路径选项
            context_menu.add_command(
                label="复制文件路径",
                command=lambda: self.copy_file_path(file_path)
            )

            # 添加复制文件名选项
            filename = os.path.basename(file_path)
            context_menu.add_command(
                label="复制文件名",
                command=lambda: self.copy_filename(filename)
            )

            # 添加分隔线
            context_menu.add_separator()

            # 添加重命名选项
            context_menu.add_command(
                label="重命名文件",
                command=lambda: self.rename_file(file_path, size_key)
            )

            # 显示菜单
            context_menu.post(event.x_root, event.y_root)

        except Exception as e:
            print(f"显示右键菜单时出错: {str(e)}")

    def copy_file_path(self, file_path):
        """复制文件路径到剪贴板"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(file_path)
            self.show_status(f"已复制文件路径: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"复制文件路径失败: {str(e)}")
            self.show_status(f"复制失败: {str(e)}")

    def copy_filename(self, filename):
        """复制文件名到剪贴板"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(filename)
            self.show_status(f"已复制文件名: {filename}")
        except Exception as e:
            print(f"复制文件名失败: {str(e)}")
            self.show_status(f"复制失败: {str(e)}")

    def rename_file(self, file_path, size_key):
        """重命名文件"""
        try:
            if not os.path.exists(file_path):
                messagebox.showerror("错误", "文件不存在，无法重命名")
                return

            # 获取当前文件名和目录
            current_dir = os.path.dirname(file_path)
            current_filename = os.path.basename(file_path)
            current_name, current_ext = os.path.splitext(current_filename)

            # 创建重命名对话框
            rename_dialog = tk.Toplevel(self.root)
            rename_dialog.title("重命名文件")
            rename_dialog.geometry("400x150")
            rename_dialog.resizable(False, False)
            rename_dialog.transient(self.root)
            rename_dialog.grab_set()

            # 居中显示
            rename_dialog.update_idletasks()
            x = (rename_dialog.winfo_screenwidth() // 2) - (rename_dialog.winfo_width() // 2)
            y = (rename_dialog.winfo_screenheight() // 2) - (rename_dialog.winfo_height() // 2)
            rename_dialog.geometry(f"+{x}+{y}")

            # 创建界面元素
            frame = ttk.Frame(rename_dialog, padding="20")
            frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(frame, text="新文件名:").pack(anchor=tk.W, pady=(0, 5))

            # 文件名输入框
            name_var = tk.StringVar(value=current_name)
            name_entry = ttk.Entry(frame, textvariable=name_var, width=40)
            name_entry.pack(fill=tk.X, pady=(0, 5))
            name_entry.select_range(0, tk.END)
            name_entry.focus()

            # 扩展名显示
            ttk.Label(frame, text=f"扩展名: {current_ext}").pack(anchor=tk.W, pady=(0, 10))

            # 按钮框架
            button_frame = ttk.Frame(frame)
            button_frame.pack(fill=tk.X)

            result = {'confirmed': False, 'new_name': ''}

            def confirm_rename():
                new_name = name_var.get().strip()
                if not new_name:
                    messagebox.showerror("错误", "文件名不能为空")
                    return

                # 检查文件名是否包含非法字符
                invalid_chars = '<>:"/\\|?*'
                if any(char in new_name for char in invalid_chars):
                    messagebox.showerror("错误", f"文件名不能包含以下字符: {invalid_chars}")
                    return

                result['confirmed'] = True
                result['new_name'] = new_name
                rename_dialog.destroy()

            def cancel_rename():
                rename_dialog.destroy()

            ttk.Button(button_frame, text="确定", command=confirm_rename).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="取消", command=cancel_rename).pack(side=tk.LEFT)

            # 绑定回车键
            rename_dialog.bind('<Return>', lambda e: confirm_rename())
            rename_dialog.bind('<Escape>', lambda e: cancel_rename())

            # 等待对话框关闭
            self.root.wait_window(rename_dialog)

            # 处理重命名
            if result['confirmed']:
                new_filename = result['new_name'] + current_ext
                new_file_path = os.path.join(current_dir, new_filename)

                # 检查新文件名是否已存在
                if os.path.exists(new_file_path) and new_file_path != file_path:
                    messagebox.showerror("错误", "目标文件名已存在")
                    return

                # 执行重命名
                os.rename(file_path, new_file_path)

                # 更新内存中的数据
                if size_key in self.current_results:
                    file_list = self.current_results[size_key]
                    if file_path in file_list:
                        index = file_list.index(file_path)
                        file_list[index] = new_file_path

                # 刷新显示
                self.display_results(self.current_results)

                # 更新结果文件
                self.update_result_file()

                self.show_status(f"文件重命名成功: {new_filename}")

        except Exception as e:
            print(f"重命名文件失败: {str(e)}")
            messagebox.showerror("错误", f"重命名文件失败:\n{str(e)}")

    def on_closing(self):
        self.stop_video_playback()
        if self.tooltip:
            self.tooltip.destroy()
        self.root.destroy()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.last_directory = config.get('last_directory', '')
                    self.last_loaded_file = config.get('last_loaded_file', '')
        except Exception:
            # 如果加载失败，使用默认值
            self.last_directory = ""
            self.last_loaded_file = ""

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'last_directory': self.current_directory,
                'last_loaded_file': self.last_loaded_file
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception:
            pass  # 如果保存失败，静默处理

    def save_results(self):
        if not self.current_results:
            messagebox.showwarning("警告", "没有可保存的搜索结果")
            return
            
        # 使用固定的默认文件名
        default_filename = "same_size_files.json"
        
        try:
            # 确保初始目录存在且可访问
            initial_dir = os.path.expanduser("~")  # 使用用户主目录作为默认目录
            
            # 强制窗口在最前面
            self.root.attributes('-topmost', True)
            self.root.update()
            
            try:
                file_path = filedialog.asksaveasfilename(
                    title="保存搜索结果",
                    initialdir=initial_dir,
                    initialfile=default_filename,
                    defaultextension=".json",
                    filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                    parent=self.root
                )
            finally:
                # 恢复窗口正常状态
                self.root.attributes('-topmost', False)
            
            if not file_path:  # 用户取消保存
                return
                
            # 确保路径是字符串类型
            file_path = str(file_path)
            
            # 准备保存的数据
            save_data = {
                "directory": self.current_directory,
                "timestamp": datetime.datetime.now().isoformat(),
                "results": self.current_results
            }
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            # 保存最后加载的文件路径
            self.last_loaded_file = file_path
            self.save_config()
            
            messagebox.showinfo("成功", "搜索结果已保存")
        except Exception as e:
            print("保存文件时出错:", str(e))  # 打印详细错误信息
            messagebox.showerror("错误", f"保存文件时出错：\n{str(e)}")

    def load_results(self):
        try:
            file_path = filedialog.askopenfilename(
                title="加载搜索结果",
                initialdir="",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                parent=self.root
            )
            
            if not file_path:  # 用户取消选择
                return
                
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 更新当前目录
            self.current_directory = data.get("directory", "")
            self.path_var.set(f"当前文件夹: {self.current_directory}")
            
            # 获取结果数据
            loaded_results = data.get("results", {})
            
            # 检查每个大小组中的文件是否存在，并移除不存在的文件
            self.current_results = {}
            for size, files in loaded_results.items():
                # 过滤出存在的文件
                existing_files = [f for f in files if os.path.exists(f)]
                # 只保留有两个或以上文件的组
                if len(existing_files) >= 2:
                    # 尝试将键转换为数字（可能是文件大小或时长）
                    try:
                        # 如果是数字字符串，转换为数字
                        if isinstance(size, str) and size.replace('.', '').isdigit():
                            numeric_key = float(size) if '.' in size else int(size)
                        else:
                            numeric_key = size
                        self.current_results[numeric_key] = existing_files
                    except (ValueError, TypeError):
                        # 如果转换失败，保持原样
                        self.current_results[size] = existing_files
            
            # 显示结果
            if self.current_results:
                self.display_results(self.current_results)
                # 启用保存按钮
                self.save_btn.configure(state='normal')
                # 保存最后加载的文件路径
                self.last_loaded_file = file_path
                self.save_config()
                messagebox.showinfo("成功", "搜索结果已加载")
            else:
                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(tk.END, "未找到大小相同的文件\n")
                self.save_btn.configure(state='disabled')
                messagebox.showwarning("警告", "加载的结果中没有找到有效的相同大小文件组")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载文件时出错：{str(e)}")

    def show_status(self, message, clear_after=3000):
        """在状态栏显示消息，并在指定时间后清除"""
        self.status_var.set(message)
        if clear_after:
            self.root.after(clear_after, lambda: self.status_var.set(""))

    def log_delete(self, file_path, success=True, error_msg=None):
        """记录文件删除操作"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            status = "成功" if success else "失败"
            log_msg = f"[{timestamp}] 删除{status}: {file_path}"
            if error_msg:
                log_msg += f" (错误: {error_msg})"
            
            # 确保日志文件所在目录存在
            log_dir = os.path.dirname(self.delete_log_file)
            if not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir)
                except Exception as e:
                    print(f"创建日志目录失败: {str(e)}")
                    # 如果无法创建目录，尝试直接写入文件
            
            # 写入日志文件
            try:
                with open(self.delete_log_file, 'a', encoding='utf-8') as f:
                    f.write(log_msg + "\n")
            except Exception as e:
                print(f"写入删除日志失败: {str(e)}")
                # 如果写入失败，显示在状态栏
                self.show_status(f"写入删除日志失败: {str(e)}")
        except Exception as e:
            print(f"记录删除日志时出错: {str(e)}")
            self.show_status(f"记录删除日志时出错: {str(e)}")

    def delete_file(self, file_path, size):
        try:
            # 在删除操作前主动停止视频预览以提高性能
            if self.is_playing or self._current_preview:
                print("删除操作前停止视频预览以提高性能")
                self.stop_preview()

            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.current_results[size].remove(file_path)
                if len(self.current_results[size]) < 2:
                    del self.current_results[size]
                # 使用缓存恢复而不是重新扫描
                if not self.load_from_cache_after_delete([file_path]):
                    self.display_results(self.current_results)
                self.log_delete(file_path, False, "文件不存在")
                return

            try:
                os.remove(file_path)
                # 记录删除成功
                self.log_delete(file_path)
                # 显示删除成功状态
                self.show_status(f"文件删除成功")
            except Exception as e:
                error_msg = str(e)
                print(f"删除文件失败: {error_msg}")
                # 记录删除失败
                self.log_delete(file_path, False, error_msg)
                messagebox.showerror("错误", f"删除文件失败：\n{error_msg}")
                return

            # 检查当前预览的文件是否属于这个组
            current_preview_in_group = False
            if self._current_preview and size in self.current_results:
                current_preview_in_group = self._current_preview in self.current_results[size]

            # 更新数据
            self.current_results[size].remove(file_path)
            group_deleted = False
            if len(self.current_results[size]) < 2:
                del self.current_results[size]
                group_deleted = True

            # 如果当前预览的文件属于被删除的组，停止预览
            if current_preview_in_group and group_deleted:
                self.stop_preview()
                self.show_status("该组已被移除，预览已停止")

            # 使用缓存恢复而不是重新扫描
            if not self.load_from_cache_after_delete([file_path]):
                self.display_results(self.current_results)

            # 更新结果文件
            self.update_result_file()

        except Exception as e:
            error_msg = str(e)
            print(f"删除文件出错: {error_msg}")
            # 记录删除失败
            self.log_delete(file_path, False, error_msg)
            messagebox.showerror("错误", f"删除文件出错：\n{error_msg}")

    def update_result_file(self):
        """更新结果文件"""
        if self.last_loaded_file and os.path.exists(self.last_loaded_file):
            try:
                save_data = {
                    "directory": self.current_directory,
                    "timestamp": datetime.datetime.now().isoformat(),
                    "results": self.current_results
                }
                with open(self.last_loaded_file, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"更新结果文件失败: {str(e)}")

    def on_label_enter(self, event, label):
        if label != self.selected_label:
            label.configure(style="Highlight.TLabel")

    def on_label_leave(self, event, label):
        if label != self.selected_label:
            label.configure(style="TLabel")

    def on_label_click(self, event, label, file_path):
        # 取消之前选中标签的高亮
        if self.selected_label:
            self.selected_label.configure(style="TLabel")
        
        # 设置新选中标签的高亮
        label.configure(style="Selected.TLabel")
        self.selected_label = label
        
        # 预览文件
        self.preview_file(file_path)

    def display_results(self, results, focus_file=None):
        """显示搜索结果"""
        # 在显示前确保清理只有1个文件的组
        self.cleanup_single_file_groups(results)

        # 先清空文本区域
        self.text_area.delete(1.0, tk.END)
        
        # 清除之前的勾选框变量
        self.checkbox_vars.clear()
        
        if not results:
            self.text_area.insert(tk.END, "未找到相同的文件\n")
            return

        # 清除之前的删除按钮
        for button in self.delete_buttons.values():
            button.destroy()
        self.delete_buttons.clear()

        # 重置选中的标签
        self.selected_label = None

        # 判断搜索类型并显示相应标题
        first_key = next(iter(results.keys()))
        # 通过检查第一个文件的扩展名来判断搜索类型
        first_files = next(iter(results.values()))
        if first_files:
            first_file = first_files[0]
            file_ext = os.path.splitext(first_file)[1].lower()
            video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob', '.asf', '.rm', '.rmvb'}

            # 如果是视频文件且key值较小（通常时长不会超过几万秒），则是时长搜索
            if file_ext in video_extensions and isinstance(first_key, (int, float)) and first_key < 100000:
                self.text_area.insert(tk.END, "找到以下时长相同的文件组：\n")
                is_duration_search = True
            else:
                self.text_area.insert(tk.END, "找到以下大小相同的文件组：\n")
                is_duration_search = False
        else:
            self.text_area.insert(tk.END, "找到以下大小相同的文件组：\n")
            is_duration_search = False

        # 创建分隔符
        separator = "=" * 50

        # 根据搜索类型对结果进行排序
        if is_duration_search:
            # 按时长搜索：从长到短排列（降序）
            sorted_results = sorted(results.items(), key=lambda x: float(x[0]) if isinstance(x[0], (int, float, str)) and str(x[0]).replace('.', '').isdigit() else 0, reverse=True)
            print(f"按时长排序：从长到短，共 {len(sorted_results)} 组")
        else:
            # 按大小搜索：从大到小排列（降序）
            sorted_results = sorted(results.items(), key=lambda x: int(x[0]) if isinstance(x[0], (int, str)) and str(x[0]).isdigit() else 0, reverse=True)
            print(f"按大小排序：从大到小，共 {len(sorted_results)} 组")

        # 遍历排序后的所有组
        for i, (key, files) in enumerate(sorted_results):
            # 如果不是第一组，先添加分隔符
            if i > 0:
                self.text_area.insert(tk.END, f"\n{separator}\n")

            # 根据搜索类型显示不同的信息
            if is_duration_search:
                try:
                    # 确保key是数字类型
                    duration_seconds = float(key) if isinstance(key, str) else key
                    minutes = int(duration_seconds // 60)
                    seconds = int(duration_seconds % 60)
                    self.text_area.insert(tk.END, f"\n视频时长: {minutes}分{seconds}秒 ({duration_seconds}秒)\n")
                except (ValueError, TypeError):
                    # 如果转换失败，直接显示原值
                    self.text_area.insert(tk.END, f"\n视频时长: {key}\n")
            else:
                self.text_area.insert(tk.END, f"\n文件大小: {key} 字节\n")
            for file in files:
                # 创建勾选框变量
                var = tk.BooleanVar()
                self.checkbox_vars[file] = var
                
                # 创建勾选框，添加回调函数
                checkbox = ttk.Checkbutton(self.text_area, variable=var, 
                                         command=lambda f=file, v=var: self.on_checkbox_click(f, v))
                self.text_area.window_create(tk.END, window=checkbox)
                
                # 获取文件显示信息（使用缓存优化）
                try:
                    display_info = self.get_file_display_info_cached(file)
                    file_info = display_info['file_info']

                    # 插入文件信息
                    self.text_area.insert(tk.END, f"  - {file_info}")

                except Exception as e:
                    # 获取显示信息失败时的后备方案
                    print(f"获取文件显示信息失败 {file}: {str(e)}")
                    display_path = os.path.basename(file)
                    file_info = f"{display_path} (获取信息失败)"
                    self.text_area.insert(tk.END, f"  - {file_info}")

                # 添加制表符来分隔文件名和按钮
                self.text_area.insert(tk.END, "\t")

                # 在固定位置创建删除按钮
                delete_btn = ttk.Button(self.text_area, text="删除",
                                      command=lambda f=file, s=key: self.delete_file(f, s))
                self.text_area.window_create(tk.END, window=delete_btn)
                self.delete_buttons[file] = delete_btn

                # 创建移出按钮
                remove_btn = ttk.Button(self.text_area, text="移出",
                                      command=lambda f=file, s=key: self.remove_single_file(f, s))
                self.text_area.window_create(tk.END, window=remove_btn)
                
                # 插入换行
                self.text_area.insert(tk.END, "\n")
                
                # 为文件路径添加标签和绑定事件
                line_start = self.text_area.index("end-2c linestart")
                line_end = self.text_area.index("end-2c lineend")
                path_start = f"{line_start}+3c"  # 跳过勾选框和"  -"
                path_end = f"{line_end}-5c"  # 减去删除按钮的空间
                
                # 添加点击事件标签
                tag_name = f"file_{file}"
                self.text_area.tag_add(tag_name, path_start, path_end)
                self.text_area.tag_configure(tag_name, foreground="blue", underline=1)
                
                # 绑定点击事件
                def create_click_handler(file_path):
                    def handler(event):
                        if event.num == 1:  # 只响应左键点击
                            self.preview_file(file_path)
                    return handler
                
                # 绑定鼠标事件
                self.text_area.tag_bind(tag_name, "<Button-1>", create_click_handler(file))

                # 绑定右键菜单事件
                def create_right_click_handler(file_path, size_key):
                    def handler(event):
                        self.show_file_context_menu(event, file_path, size_key)
                    return handler

                self.text_area.tag_bind(tag_name, "<Button-3>", create_right_click_handler(file, key))

                # 绑定鼠标悬停事件，显示完整路径工具提示
                def create_enter_handler(tag, full_path):
                    def handler(event):
                        if self.text_area.tag_ranges(tag):  # 确保标签仍然存在
                            self.text_area.config(cursor="hand2")
                            # 显示完整路径的工具提示
                            self.show_tooltip(event, full_path)
                    return handler

                def create_leave_handler():
                    def handler(event):
                        self.text_area.config(cursor="")
                        # 隐藏工具提示
                        self.hide_tooltip()
                    return handler

                self.text_area.tag_bind(tag_name, "<Enter>", create_enter_handler(tag_name, file))
                self.text_area.tag_bind(tag_name, "<Leave>", create_leave_handler())
        
        # 更新按钮状态
        if results:
            self.select_all_btn.configure(state='normal')
            self.deselect_all_btn.configure(state='normal')
            self.delete_selected_btn.configure(state='normal')
            self.remove_from_list_btn.configure(state='normal')
            self.save_btn.configure(state='normal')
        else:
            self.select_all_btn.configure(state='disabled')
            self.deselect_all_btn.configure(state='disabled')
            self.delete_selected_btn.configure(state='disabled')
            self.remove_from_list_btn.configure(state='disabled')
            self.save_btn.configure(state='disabled')
            
        # 绑定鼠标滚轮事件
        self.text_area.bind("<MouseWheel>", self.on_mousewheel)
        self.text_area.bind("<Button-4>", self.on_mousewheel)
        self.text_area.bind("<Button-5>", self.on_mousewheel)

    def on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # 根据不同系统处理滚动
        if event.num == 4:
            self.text_area.yview_scroll(-1, "units")
        elif event.num == 5:
            self.text_area.yview_scroll(1, "units")
        else:
            # Windows的鼠标滚轮事件
            if event.delta > 0:
                self.text_area.yview_scroll(-1, "units")
            else:
                self.text_area.yview_scroll(1, "units")

    def select_directory(self):
        try:
            # 确保主窗口在最前面
            self.root.lift()
            self.root.focus_force()
            self.root.update()
            
            # 创建一个临时的顶层窗口作为文件对话框的父窗口
            dialog_parent = tk.Toplevel(self.root)
            dialog_parent.withdraw()  # 隐藏临时窗口
            
            try:
                directory = filedialog.askdirectory(
                    title="请选择要搜索的文件夹",
                    initialdir=self.current_directory if self.current_directory else "",
                    parent=dialog_parent
                )
            finally:
                dialog_parent.destroy()  # 确保临时窗口被销毁
            
            if directory:
                self.current_directory = directory
                self.path_var.set(f"当前文件夹: {directory}")
                # 启用搜索按钮
                self.search_by_size_btn.configure(state='normal')
                self.search_by_duration_btn.configure(state='normal')
                # 清空之前的结果
                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(tk.END, "请选择搜索方式：相同大小 或 相同时长\n")
                self.progress_var.set(0)
                self.current_file_var.set("已选择文件夹，请选择搜索方式")
                # 保存配置
                self.save_config()
        except Exception as e:
            print(f"选择文件夹时出错: {str(e)}")
            self.show_dialog('error', "错误", f"选择文件夹时出错：{str(e)}")

    def search_by_size(self):
        if not self.current_directory:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        # 禁用搜索按钮
        self.search_by_size_btn.configure(state='disabled')
        self.search_by_duration_btn.configure(state='disabled')

        self.text_area.delete(1.0, tk.END)
        self.text_area.insert(tk.END, f"开始按大小搜索文件夹: {self.current_directory}\n")
        self.text_area.see(tk.END)

        # 在新线程中运行搜索
        threading.Thread(target=self._search_by_size_worker, daemon=True).start()

    def _search_by_size_worker(self):
        # 定义视频文件扩展名
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob', '.asf', '.rm', '.rmvb'}
        # 定义图片文件扩展名
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.ico', '.svg', '.raw', '.cr2', '.nef', '.arw', '.dng'}

        # 根据勾选框状态确定要搜索的文件类型
        include_images = self.include_images_var.get()
        allowed_extensions = video_extensions.copy()
        if include_images:
            allowed_extensions.update(image_extensions)

        size_dict = defaultdict(list)
        total_files = sum([len(files) for _, _, files in os.walk(self.current_directory)])
        processed_files = 0

        def update_progress(processed, total, current_file):
            """线程安全的进度更新函数"""
            progress = (processed / total) * 100
            self.root.after(0, lambda: self.progress_var.set(progress))
            self.root.after(0, lambda: self.current_file_var.set(f"正在处理: {current_file}"))

        for root, dirs, files in os.walk(self.current_directory):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    # 检查文件扩展名
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext not in allowed_extensions:
                        processed_files += 1
                        # 更新进度（线程安全）
                        update_progress(processed_files, total_files, file_path)
                        continue

                    file_size = self.get_file_size_cached(file_path)
                    if file_size is not None:
                        size_dict[file_size].append(file_path)

                    # 更新进度
                    processed_files += 1
                    update_progress(processed_files, total_files, file_path)

                except (OSError, PermissionError):
                    processed_files += 1
                    update_progress(processed_files, total_files, file_path)
                    continue

        # 显示结果（线程安全）
        same_size_groups = {size: paths for size, paths in size_dict.items() if len(paths) > 1}
        self.current_results = same_size_groups

        def finish_search():
            """完成搜索的GUI更新"""
            # 缓存搜索结果
            self._scan_results_cache = same_size_groups.copy()
            self._last_scan_directory = self.current_directory
            self._last_scan_timestamp = time.time()

            self.display_results(same_size_groups)
            self.current_file_var.set("按大小搜索完成")
            self.progress_var.set(100)  # 确保进度条显示100%
            # 重新启用搜索按钮
            self.search_by_size_btn.configure(state='normal')
            self.search_by_duration_btn.configure(state='normal')
            if same_size_groups:
                self.save_btn.configure(state='normal')

        # 在主线程中执行GUI更新
        self.root.after(0, finish_search)

    def init_network_drives(self):
        """初始化网络驱动器访问"""
        try:
            # 获取当前用户的网络驱动器列表
            resume = 0
            while True:
                try:
                    drives, total, resume = win32net.NetUseEnum(None, 0, resume)
                    if not resume:
                        break
                except win32net.error:
                    break
                
            # 确保每个网络驱动器都可以访问
            for drive in drives:
                if drive['remote'] and drive['local']:
                    try:
                        os.listdir(drive['local'])
                    except:
                        pass
        except:
            pass

    def search_by_duration(self):
        if not self.current_directory:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        # 禁用搜索按钮
        self.search_by_size_btn.configure(state='disabled')
        self.search_by_duration_btn.configure(state='disabled')

        self.text_area.delete(1.0, tk.END)
        self.text_area.insert(tk.END, f"开始按时长搜索文件夹: {self.current_directory}\n")
        self.text_area.see(tk.END)

        # 在新线程中运行搜索
        threading.Thread(target=self._search_by_duration_worker, daemon=True).start()

    def _search_by_duration_worker(self):
        # 只搜索视频文件（图片文件没有时长概念）
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob', '.asf', '.rm', '.rmvb'}

        duration_dict = defaultdict(list)
        total_files = sum([len(files) for _, _, files in os.walk(self.current_directory)])
        processed_files = 0

        def update_progress(processed, total, current_file):
            """线程安全的进度更新函数"""
            progress = (processed / total) * 100
            self.root.after(0, lambda: self.progress_var.set(progress))
            self.root.after(0, lambda: self.current_file_var.set(f"正在处理: {current_file}"))

        for root, dirs, files in os.walk(self.current_directory):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    # 检查文件扩展名
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext not in video_extensions:
                        processed_files += 1
                        # 更新进度（线程安全）
                        update_progress(processed_files, total_files, file_path)
                        continue

                    # 获取视频时长（显示详细进度）
                    update_progress(processed_files, total_files, f"解析时长: {os.path.basename(file_path)}")

                    duration = self.get_video_duration(file_path)
                    if duration is not None and duration > 0:
                        # 将时长四舍五入到秒，避免微小差异
                        duration_rounded = round(duration)
                        duration_dict[duration_rounded].append(file_path)
                        print(f"添加到时长组 {duration_rounded}秒: {file_path}")
                    else:
                        print(f"跳过文件（无法获取时长）: {file_path}")

                    # 更新进度
                    processed_files += 1
                    update_progress(processed_files, total_files, f"已处理: {os.path.basename(file_path)}")

                except (OSError, PermissionError):
                    processed_files += 1
                    update_progress(processed_files, total_files, file_path)
                    continue

        # 显示结果（线程安全）
        same_duration_groups = {duration: paths for duration, paths in duration_dict.items() if len(paths) > 1}
        self.current_results = same_duration_groups

        def finish_search():
            """完成搜索的GUI更新"""
            # 缓存搜索结果
            self._scan_results_cache = same_duration_groups.copy()
            self._last_scan_directory = self.current_directory
            self._last_scan_timestamp = time.time()

            self.display_results(same_duration_groups)
            self.current_file_var.set("按时长搜索完成")
            self.progress_var.set(100)  # 确保进度条显示100%
            # 重新启用搜索按钮
            self.search_by_size_btn.configure(state='normal')
            self.search_by_duration_btn.configure(state='normal')
            if same_duration_groups:
                self.save_btn.configure(state='normal')

        # 在主线程中执行GUI更新
        self.root.after(0, finish_search)

    def get_video_duration(self, video_path):
        """获取视频文件的时长（秒）- 带缓存优化"""
        try:
            # 先检查缓存
            cached_info = self.get_cached_file_info(video_path)
            current_mtime = os.path.getmtime(video_path)

            if (cached_info and 'duration' in cached_info and
                cached_info.get('last_modified') == current_mtime):
                # 缓存命中，直接返回
                return cached_info['duration']

            # 缓存未命中，需要解析视频
            print(f"解析视频时长: {os.path.basename(video_path)}")

            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"无法打开视频文件: {video_path}")
                # 缓存失败结果，避免重复尝试
                file_info = cached_info or {}
                file_info['duration'] = None
                file_info['last_modified'] = current_mtime
                file_info['parse_failed'] = True
                self.cache_file_info(video_path, file_info)
                return None

            # 优化：先尝试直接获取时长属性
            duration_ms = cap.get(cv2.CAP_PROP_POS_MSEC)
            total_frames = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            fps = cap.get(cv2.CAP_PROP_FPS)

            # 尝试多种方法获取时长
            duration = None

            # 方法1：使用帧数和帧率计算
            if fps > 0 and total_frames > 0:
                duration = total_frames / fps
                print(f"方法1成功 - 帧数: {total_frames}, 帧率: {fps}, 时长: {duration:.2f}秒")

            # 方法2：如果方法1失败，尝试seek到最后
            if duration is None or duration <= 0:
                try:
                    # 跳到最后一帧
                    cap.set(cv2.CAP_PROP_POS_AVI_RATIO, 1)
                    duration_ms = cap.get(cv2.CAP_PROP_POS_MSEC)
                    if duration_ms > 0:
                        duration = duration_ms / 1000.0
                        print(f"方法2成功 - 时长: {duration:.2f}秒")
                except:
                    pass

            cap.release()

            if duration is not None and duration > 0:
                # 缓存成功结果
                file_info = cached_info or {}
                file_info['duration'] = duration
                file_info['last_modified'] = current_mtime
                file_info['parse_failed'] = False
                self.cache_file_info(video_path, file_info)

                print(f"✓ 获取视频时长成功: {duration:.2f}秒")
                return duration
            else:
                print(f"✗ 无法获取有效时长: {video_path}")
                # 缓存失败结果
                file_info = cached_info or {}
                file_info['duration'] = None
                file_info['last_modified'] = current_mtime
                file_info['parse_failed'] = True
                self.cache_file_info(video_path, file_info)
                return None

        except Exception as e:
            print(f"获取视频时长失败 {video_path}: {str(e)}")
            # 缓存异常结果
            try:
                file_info = cached_info or {}
                file_info['duration'] = None
                file_info['last_modified'] = os.path.getmtime(video_path)
                file_info['parse_failed'] = True
                file_info['error'] = str(e)
                self.cache_file_info(video_path, file_info)
            except:
                pass
            return None

    def get_file_size_cached(self, file_path):
        """获取文件大小 - 带缓存优化"""
        try:
            # 先检查缓存
            cached_info = self.get_cached_file_info(file_path)
            current_mtime = os.path.getmtime(file_path)

            if (cached_info and 'size' in cached_info and
                cached_info.get('last_modified') == current_mtime):
                return cached_info['size']

            # 获取文件大小
            file_size = os.path.getsize(file_path)

            # 缓存结果
            file_info = cached_info or {}
            file_info['size'] = file_size
            file_info['last_modified'] = current_mtime
            self.cache_file_info(file_path, file_info)

            return file_size

        except Exception as e:
            print(f"获取文件大小失败 {file_path}: {str(e)}")
            return None

    def get_file_display_info_cached(self, file_path):
        """获取文件显示信息 - 带缓存优化"""
        try:
            # 检查显示信息缓存
            cache_key = f"{file_path}_{self.current_directory}"
            if cache_key in self._display_info_cache:
                cached_display = self._display_info_cache[cache_key]
                # 检查文件是否仍然存在且未修改
                if os.path.exists(file_path):
                    current_mtime = os.path.getmtime(file_path)
                    if cached_display.get('last_modified') == current_mtime:
                        return cached_display

            # 计算显示信息
            display_info = {}

            # 计算相对路径
            if self.current_directory and os.path.isabs(file_path):
                try:
                    relative_path = os.path.relpath(file_path, self.current_directory)
                    if relative_path.startswith('..'):
                        relative_path = file_path
                except ValueError:
                    relative_path = file_path
            else:
                relative_path = file_path

            # 截短文件名处理
            max_path_length = 50
            display_path = relative_path
            if len(relative_path) > max_path_length:
                start_len = 20
                end_len = max_path_length - start_len - 3
                display_path = relative_path[:start_len] + "..." + relative_path[-end_len:]

            display_info['display_path'] = display_path
            display_info['relative_path'] = relative_path

            # 获取文件大小和格式化字符串
            if os.path.exists(file_path):
                file_size = self.get_file_size_cached(file_path)
                if file_size is not None:
                    size_str = self.format_size(file_size)
                    display_info['file_info'] = f"{display_path} ({size_str})"
                    display_info['exists'] = True
                else:
                    display_info['file_info'] = f"{display_path} (无法获取大小)"
                    display_info['exists'] = True
                display_info['last_modified'] = os.path.getmtime(file_path)
            else:
                display_info['file_info'] = f"{display_path} (文件不存在)"
                display_info['exists'] = False
                display_info['last_modified'] = 0

            # 缓存结果
            self._display_info_cache[cache_key] = display_info

            return display_info

        except Exception as e:
            print(f"获取文件显示信息失败 {file_path}: {str(e)}")
            # 返回基本信息
            return {
                'display_path': os.path.basename(file_path),
                'file_info': f"{os.path.basename(file_path)} (获取信息失败)",
                'exists': False,
                'last_modified': 0
            }

    def show_dialog(self, dialog_type, title, message, **kwargs):
        """统一的对话框显示方法"""
        try:
            print(f"正在显示对话框: 类型={dialog_type}, 标题={title}")
            print(f"消息内容: {message}")
            
            # 直接使用系统对话框
            try:
                if dialog_type == 'error':
                    messagebox.showerror(title, message)
                elif dialog_type == 'warning':
                    messagebox.showwarning(title, message)
                elif dialog_type == 'info':
                    messagebox.showinfo(title, message)
                elif dialog_type == 'yesno':
                    return messagebox.askyesno(title, message)
                else:
                    raise ValueError(f"未知的对话框类型: {dialog_type}")
            except Exception as e:
                print(f"显示对话框时出错: {str(e)}")
                return False
                
        except Exception as e:
            print(f"显示对话框出错: {str(e)}")
            return False

    def select_all_files(self):
        """选择所有文件"""
        for var in self.checkbox_vars.values():
            var.set(1)
        # 更新选中文件的显示
        self.update_selected_files_tree()

    def deselect_all_files(self):
        """取消选择所有文件"""
        for var in self.checkbox_vars.values():
            var.set(0)
        # 更新选中文件的显示
        self.update_selected_files_tree()

    def delete_selected_files(self):
        """删除所有选中的文件"""
        try:
            selected_files = []
            for file_path, var in self.checkbox_vars.items():
                if var.get():
                    selected_files.append(file_path)
            
            if not selected_files:
                self.show_status("请先选择要删除的文件")
                return

            # 在批量删除操作前主动停止视频预览以提高性能
            if self.is_playing or self._current_preview:
                print("批量删除操作前停止视频预览以提高性能")
                self.stop_preview()
            
            # 批量删除文件
            deleted_count = 0
            failed_files = []
            for file_path in selected_files:
                try:
                    # 获取文件大小
                    size = None
                    for s, files in self.current_results.items():
                        if file_path in files:
                            size = s
                            break
                    
                    if size is None:
                        continue
                    
                    # 删除文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted_count += 1
                        # 记录删除成功
                        self.log_delete(file_path)
                        
                        # 从结果中移除
                        self.current_results[size].remove(file_path)
                        if len(self.current_results[size]) < 2:
                            del self.current_results[size]
                        
                except Exception as e:
                    error_msg = str(e)
                    print(f"删除文件 {file_path} 时出错: {error_msg}")
                    # 记录删除失败
                    self.log_delete(file_path, False, error_msg)
                    failed_files.append(file_path)
            
            # 批量更新完成后再更新界面
            if deleted_count > 0:
                # 检查当前预览的文件是否还存在于结果中
                if self._current_preview:
                    preview_still_exists = False
                    for size, files in self.current_results.items():
                        if self._current_preview in files:
                            preview_still_exists = True
                            break

                    # 如果当前预览的文件不再存在于任何组中，停止预览
                    if not preview_still_exists:
                        self.stop_preview()
                        self.show_status("预览文件已被删除，预览已停止")

                # 使用缓存恢复而不是重新扫描
                successfully_deleted = [f for f in selected_files if f not in failed_files]
                if not self.load_from_cache_after_delete(successfully_deleted):
                    self.display_results(self.current_results)

                # 显示删除成功提示
                success_message = f"成功删除 {deleted_count} 个文件"
                if failed_files:
                    success_message += f" ({len(failed_files)} 个文件删除失败)"
                self.root.after(100, lambda: messagebox.showinfo("删除完成", success_message))

                # 更新结果文件
                self.update_result_file()
            elif failed_files:
                self.show_status(f"删除失败: 所有 {len(failed_files)} 个文件都无法删除")
                        
        except Exception as e:
            error_msg = str(e)
            print(f"批量删除文件时出错: {error_msg}")
            self.show_status(error_msg)

    def remove_selected_from_list(self):
        """从结果列表中移除选中的文件（不删除实际文件）"""
        try:
            selected_files = []
            for file_path, var in self.checkbox_vars.items():
                if var.get():
                    selected_files.append(file_path)
            
            if not selected_files:
                self.show_status("请先选择要移除的文件")
                return

            # 在移出列表操作前主动停止视频预览以提高性能
            if self.is_playing or self._current_preview:
                print("移出列表操作前停止视频预览以提高性能")
                self.stop_preview()

            # 从结果中移除文件
            removed_count = 0
            for file_path in selected_files:
                # 获取文件大小
                size = None
                for s, files in list(self.current_results.items()):
                    if file_path in files:
                        size = s
                        break
                
                if size is not None:
                    # 从结果中移除
                    self.current_results[size].remove(file_path)
                    removed_count += 1
                    
                    # 如果该大小组中的文件少于2个，删除整个大小组
                    if len(self.current_results[size]) < 2:
                        del self.current_results[size]
            
            # 更新界面
            if removed_count > 0:
                # 检查当前预览的文件是否还存在于结果中
                preview_still_exists = True
                if self._current_preview:
                    preview_still_exists = False
                    for size, files in self.current_results.items():
                        if self._current_preview in files:
                            preview_still_exists = True
                            break

                    # 如果当前预览的文件不再存在于任何组中，停止预览
                    if not preview_still_exists:
                        self.stop_preview()
                        self.show_status("预览文件已被移除，预览已停止")

                self.display_results(self.current_results)
                if not preview_still_exists:
                    # 如果预览已停止，不覆盖停止预览的状态消息
                    pass
                else:
                    self.show_status(f"已从列表中移除 {removed_count} 个文件")
                
                # 更新结果文件
                self.update_result_file()
                
                # 如果没有结果了，禁用相关按钮
                if not self.current_results:
                    self.select_all_btn.configure(state='disabled')
                    self.deselect_all_btn.configure(state='disabled')
                    self.delete_selected_btn.configure(state='disabled')
                    self.remove_from_list_btn.configure(state='disabled')
                    self.save_btn.configure(state='disabled')
                    self.text_area.delete(1.0, tk.END)
                    self.text_area.insert(tk.END, "未找到大小相同的文件\n")
                    
        except Exception as e:
            error_msg = f"移除文件出错: {str(e)}"
            print(error_msg)
            self.show_status(error_msg)

    def on_checkbox_click(self, file_path, var):
        """当勾选框状态改变时的处理"""
        # 检查是否同时启用了同目录和相同时段选择
        both_enabled = self.same_dir_var.get() and self.same_time_var.get()

        if both_enabled:
            # 同时满足同目录和相同时段的条件
            self._handle_combined_selection(file_path, var)
        elif self.same_dir_var.get():  # 如果只启用了同目录选择
            self._handle_same_directory_selection(file_path, var)
        elif self.same_time_var.get():  # 如果只启用了相同时段选择
            self._handle_same_time_selection(file_path, var)

        # 更新选中文件的显示
        self.update_selected_files_tree()

    def _handle_same_directory_selection(self, file_path, var):
        """处理同目录选择"""
        # 获取当前文件的目录
        current_file_dir = os.path.dirname(file_path)

        total_selected = 0
        selected_groups = 0

        # 遍历所有组，在每个组中选择同目录的文件
        for size, group_files in self.current_results.items():
            if len(group_files) > 1:
                # 在该组中找到与当前文件目录相同的文件
                same_dir_files_in_group = []

                for group_file in group_files:
                    group_file_dir = os.path.dirname(group_file)
                    # 检查是否与当前文件在相同目录
                    if group_file_dir == current_file_dir:
                        same_dir_files_in_group.append(group_file)

                # 如果该组中有同目录的文件，选中它们
                if same_dir_files_in_group:
                    for path in same_dir_files_in_group:
                        if path in self.checkbox_vars:
                            self.checkbox_vars[path].set(var.get())
                            if var.get():
                                total_selected += 1
                    selected_groups += 1

        # 显示选择结果
        if var.get() and total_selected > 0:
            dir_name = os.path.basename(current_file_dir) or current_file_dir
            self.show_status(f"已在 {selected_groups} 个组中选择目录 '{dir_name}' 的 {total_selected} 个文件")

    def _handle_same_time_selection(self, file_path, var):
        """处理相同时段选择"""
        # 找到当前文件所在的组
        current_group_files = None
        current_group_size = None

        for size, files in self.current_results.items():
            if file_path in files:
                current_group_files = files
                current_group_size = size
                break

        if current_group_files and len(current_group_files) > 1:
            try:
                # 获取当前组内所有文件的修改时间并排序
                current_group_times = []
                for f in current_group_files:
                    try:
                        if os.path.exists(f):
                            current_group_times.append((f, os.path.getmtime(f)))
                    except:
                        continue

                if len(current_group_times) > 1:
                    # 按时间排序
                    current_group_times.sort(key=lambda x: x[1])

                    # 找到当前文件在时间排序中的位置
                    current_file_index = None
                    for i, (f, t) in enumerate(current_group_times):
                        if f == file_path:
                            current_file_index = i
                            break

                    if current_file_index is not None:
                        # 计算当前文件在组内的时间位置比例
                        total_files_in_group = len(current_group_times)
                        time_position_ratio = current_file_index / (total_files_in_group - 1) if total_files_in_group > 1 else 0

                        # 判断是较早还是较晚的文件
                        is_early = time_position_ratio < 0.5
                        period_name = "较早" if is_early else "较晚"

                        total_selected = 0

                        # 对所有组应用相同的时间位置选择逻辑
                        for size, files in self.current_results.items():
                            if len(files) > 1:
                                # 获取该组所有文件的修改时间并排序
                                group_times = []
                                for f in files:
                                    try:
                                        if os.path.exists(f):
                                            group_times.append((f, os.path.getmtime(f)))
                                    except:
                                        continue

                                if len(group_times) > 1:
                                    # 按时间排序
                                    group_times.sort(key=lambda x: x[1])

                                    # 根据当前文件的时间位置，选择该组中相应位置的文件
                                    group_file_count = len(group_times)

                                    if is_early:
                                        # 选择较早的文件（前半部分）
                                        target_index = min(current_file_index, group_file_count - 1)
                                        # 如果当前组文件数量不同，按比例选择
                                        if group_file_count != total_files_in_group:
                                            target_index = int(time_position_ratio * (group_file_count - 1))
                                    else:
                                        # 选择较晚的文件（后半部分）
                                        target_index = max(0, min(current_file_index, group_file_count - 1))
                                        # 如果当前组文件数量不同，按比例选择
                                        if group_file_count != total_files_in_group:
                                            target_index = int(time_position_ratio * (group_file_count - 1))

                                    # 选择目标文件
                                    target_file = group_times[target_index][0]
                                    if target_file in self.checkbox_vars:
                                        self.checkbox_vars[target_file].set(var.get())
                                        if var.get():
                                            total_selected += 1

                        self.show_status(f"已在所有组中选择{period_name}时段的文件，共 {total_selected} 个文件")

            except Exception as e:
                print(f"相同时段选择时出错: {str(e)}")
                pass

    def _handle_combined_selection(self, file_path, var):
        """处理同时启用同目录和相同时段的组合选择"""
        try:
            # 获取当前文件的目录
            current_file_dir = os.path.dirname(file_path)

            # 找到当前文件所在的组
            current_group_files = None
            for size, files in self.current_results.items():
                if file_path in files:
                    current_group_files = files
                    break

            if not current_group_files or len(current_group_files) <= 1:
                return

            # 获取当前组内所有文件的修改时间并排序
            current_group_times = []
            for f in current_group_files:
                try:
                    if os.path.exists(f):
                        current_group_times.append((f, os.path.getmtime(f)))
                except:
                    continue

            if len(current_group_times) <= 1:
                return

            # 按时间排序
            current_group_times.sort(key=lambda x: x[1])

            # 找到当前文件在时间排序中的位置
            current_file_index = None
            for i, (f, t) in enumerate(current_group_times):
                if f == file_path:
                    current_file_index = i
                    break

            if current_file_index is None:
                return

            # 计算当前文件在组内的时间位置比例
            total_files_in_group = len(current_group_times)
            time_position_ratio = current_file_index / (total_files_in_group - 1) if total_files_in_group > 1 else 0

            # 判断是较早还是较晚的文件
            is_early = time_position_ratio < 0.5
            period_name = "较早" if is_early else "较晚"

            total_selected = 0
            selected_groups = 0

            # 对所有组应用组合条件：同目录 + 相同时段
            for size, files in self.current_results.items():
                if len(files) > 1:
                    # 先筛选出同目录的文件
                    same_dir_files = []
                    for f in files:
                        if os.path.dirname(f) == current_file_dir:
                            same_dir_files.append(f)

                    if len(same_dir_files) > 1:
                        # 对同目录的文件按时间排序
                        same_dir_times = []
                        for f in same_dir_files:
                            try:
                                if os.path.exists(f):
                                    same_dir_times.append((f, os.path.getmtime(f)))
                            except:
                                continue

                        if len(same_dir_times) > 1:
                            # 按时间排序
                            same_dir_times.sort(key=lambda x: x[1])

                            # 根据时间位置比例选择相应的文件
                            group_file_count = len(same_dir_times)

                            if is_early:
                                # 选择较早的文件
                                target_index = int(time_position_ratio * (group_file_count - 1))
                            else:
                                # 选择较晚的文件
                                target_index = int(time_position_ratio * (group_file_count - 1))

                            # 确保索引在有效范围内
                            target_index = max(0, min(target_index, group_file_count - 1))

                            # 选择目标文件
                            target_file = same_dir_times[target_index][0]
                            if target_file in self.checkbox_vars:
                                self.checkbox_vars[target_file].set(var.get())
                                if var.get():
                                    total_selected += 1
                                selected_groups += 1
                    elif len(same_dir_files) == 1:
                        # 如果该组只有一个同目录文件，直接选择
                        target_file = same_dir_files[0]
                        if target_file in self.checkbox_vars:
                            self.checkbox_vars[target_file].set(var.get())
                            if var.get():
                                total_selected += 1
                            selected_groups += 1

            # 显示选择结果
            if var.get() and total_selected > 0:
                dir_name = os.path.basename(current_file_dir) or current_file_dir
                self.show_status(f"已在 {selected_groups} 个组中选择目录 '{dir_name}' 的{period_name}时段文件，共 {total_selected} 个")

        except Exception as e:
            print(f"组合选择时出错: {str(e)}")
            pass

    def _find_deepest_common_directory(self, dir1, dir2):
        """找到两个目录路径的最深共同目录"""
        try:
            # 标准化路径
            dir1 = os.path.normpath(dir1)
            dir2 = os.path.normpath(dir2)

            # 如果两个目录完全相同，返回该目录
            if dir1 == dir2:
                return dir1

            # 分割路径为组件
            parts1 = dir1.split(os.sep)
            parts2 = dir2.split(os.sep)

            # 找到共同的路径组件
            common_parts = []
            min_length = min(len(parts1), len(parts2))

            for i in range(min_length):
                if parts1[i].lower() == parts2[i].lower():  # 忽略大小写比较
                    common_parts.append(parts1[i])
                else:
                    break

            # 重新组合共同路径
            if common_parts:
                return os.sep.join(common_parts)
            else:
                return ""

        except Exception as e:
            print(f"查找共同目录时出错: {str(e)}")
            return ""

    def format_size(self, size):
        """格式化文件大小显示"""
        try:
            if size >= 1024 * 1024 * 1024:  # GB
                return f"{size / (1024 * 1024 * 1024):.2f} GB"
            elif size >= 1024 * 1024:  # MB
                return f"{size / (1024 * 1024):.2f} MB"
            elif size >= 1024:  # KB
                return f"{size / 1024:.2f} KB"
            else:  # Bytes
                return f"{size} 字节"
        except:
            return str(size)

    def update_selected_files_tree(self):
        """更新选中文件的树形显示"""
        try:
            # 清空现有内容
            for item in self.selected_tree.get_children():
                self.selected_tree.delete(item)
            
            # 收集选中的文件
            selected_files = {}
            total_size = 0
            file_count = 0
            
            for file_path, var in self.checkbox_vars.items():
                if var.get():
                    # 获取文件大小
                    size = None
                    for s, files in self.current_results.items():
                        if file_path in files:
                            size = s
                            break
                    if size is not None:
                        try:
                            # 确保size是整数
                            size = int(str(size))
                            selected_files[file_path] = size
                            total_size += size
                            file_count += 1
                        except (ValueError, TypeError):
                            print(f"无效的文件大小值: {size} (文件: {file_path})")
                            continue
            
            # 更新LabelFrame标题中的统计信息
            if file_count > 0:
                frame_title = f"已选中文件 ({file_count} 个文件，{self.format_size(total_size)})"
            else:
                frame_title = "已选中文件 (0 个文件，0 字节)"
            self.selected_files_frame.configure(text=frame_title)
            
            if not selected_files:
                # 如果没有选中的文件，显示提示
                self.selected_tree.insert("", "end", text="未选择任何文件", values=("",))
                return
            
            # 构建目录树结构
            dir_structure = {}
            # 存储文件路径映射，用于在树形视图中查找原始路径
            self._file_path_mapping = {}

            for file_path, size in selected_files.items():
                try:
                    # 获取相对路径
                    try:
                        rel_path = os.path.relpath(file_path, self.current_directory)
                    except ValueError:
                        # 如果在不同驱动器，使用完整路径
                        rel_path = file_path

                    # 确保路径是字符串类型
                    if not isinstance(rel_path, str):
                        rel_path = str(rel_path)

                    # 分割路径
                    parts = os.path.normpath(rel_path).split(os.sep)

                    # 过滤掉空的路径部分
                    parts = [part for part in parts if part and part != '.']

                    # 如果没有有效的路径部分，跳过这个文件
                    if not parts:
                        continue

                    # 构建目录结构
                    current = dir_structure
                    for part in parts[:-1]:  # 处理目录部分
                        if part not in current:
                            current[part] = {"__files": []}
                        current = current[part]
                        # 确保current是字典类型
                        if not isinstance(current, dict):
                            current = {"__files": []}

                    # 确保current有__files键
                    if "__files" not in current:
                        current["__files"] = []
                    # 添加文件，同时存储原始完整路径
                    current["__files"].append((parts[-1], size, file_path))

                except Exception as e:
                    print(f"处理文件路径时出错 ({file_path}): {str(e)}")
                    continue
            
            # 递归函数用于插入节点
            def insert_nodes(parent, structure, path=""):
                # 确保structure是字典类型
                if not isinstance(structure, dict):
                    return

                for name, content in sorted(structure.items()):
                    try:
                        if name == "__files":
                            # 插入文件
                            if isinstance(content, list):
                                for file_info in sorted(content):
                                    if isinstance(file_info, tuple) and len(file_info) >= 2:
                                        if len(file_info) >= 3:
                                            # 新格式：(文件名, 大小, 完整路径)
                                            file_name, size, full_file_path = file_info[0], file_info[1], file_info[2]
                                        else:
                                            # 旧格式：(文件名, 大小)
                                            file_name, size = file_info[0], file_info[1]
                                            # 构建完整文件路径
                                            if path:
                                                full_file_path = os.path.join(self.current_directory, path, file_name)
                                            else:
                                                full_file_path = os.path.join(self.current_directory, file_name)
                                            full_file_path = os.path.normpath(full_file_path)

                                        size_str = self.format_size(size)

                                        # 插入文件节点，并在tags中存储完整路径
                                        file_item = self.selected_tree.insert(parent, "end",
                                                               text=file_name,
                                                               values=(size_str,),
                                                               tags=(full_file_path,))
                        else:
                            # 插入目录
                            folder_id = self.selected_tree.insert(parent, "end",
                                                               text=name,
                                                               values=("",))
                            # 递归处理子目录和文件
                            if isinstance(content, dict):
                                insert_nodes(folder_id, content,
                                           os.path.join(path, name) if path else name)
                    except Exception as e:
                        print(f"插入节点时出错 (name={name}): {str(e)}")
                        continue
            
            # 开始构建树
            insert_nodes("", dir_structure)
            
            # 展开所有节点
            for item in self.selected_tree.get_children():
                self.selected_tree.item(item, open=True)
        except Exception as e:
            print(f"更新文件树时出错: {str(e)}")
            self.show_status(f"更新文件树时出错: {str(e)}")

    def on_selected_tree_click(self, event):
        """处理已选中文件树的点击事件"""
        try:
            # 获取点击位置的项目
            item = self.selected_tree.identify_row(event.y)
            if not item:
                return

            # 选中该项目
            self.selected_tree.selection_set(item)

            # 处理跳转
            self._handle_tree_item_selection(item)

        except Exception as e:
            print(f"处理树形视图点击事件时出错: {str(e)}")
            self.show_status(f"点击处理失败: {str(e)}")

    def on_selected_tree_select(self, event):
        """处理已选中文件树的选择事件"""
        try:
            selection = self.selected_tree.selection()
            if not selection:
                return

            item = selection[0]
            self._handle_tree_item_selection(item)

        except Exception as e:
            print(f"处理树形视图选择事件时出错: {str(e)}")
            self.show_status(f"选择处理失败: {str(e)}")

    def _handle_tree_item_selection(self, item):
        """处理树形视图项目选择的通用逻辑"""
        try:
            # 获取项目的tags（存储了完整文件路径）
            tags = self.selected_tree.item(item, "tags")
            if not tags:
                # 如果没有tags，可能是文件夹，不处理
                return

            file_path = tags[0]  # 第一个tag是完整文件路径

            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.show_status(f"文件不存在: {os.path.basename(file_path)}")
                return

            # 在搜索结果中找到该文件所在的组
            target_size = None
            for size, files in self.current_results.items():
                if file_path in files:
                    target_size = size
                    break

            if target_size is None:
                self.show_status(f"未找到文件所在的组: {os.path.basename(file_path)}")
                return

            # 跳转到该组：滚动到对应位置并高亮显示
            self.jump_to_file_group(file_path, target_size)

        except Exception as e:
            print(f"处理树形视图项目选择时出错: {str(e)}")
            self.show_status(f"跳转失败: {str(e)}")

    def jump_to_file_group(self, file_path, target_size):
        """跳转到指定文件所在的组"""
        try:
            # 在文本区域中查找该文件
            text_content = self.text_area.get("1.0", tk.END)
            lines = text_content.split('\n')

            target_line = None
            group_start_line = None

            # 查找文件所在的行和组的开始行
            for i, line in enumerate(lines):
                # 检查是否是组标题行
                if "文件大小:" in line or "视频时长:" in line:
                    group_start_line = i + 1  # 转换为1基索引

                # 检查是否包含目标文件路径
                # 使用更精确的匹配：检查是否包含文件路径或文件名
                if (file_path in line or
                    os.path.basename(file_path) in line or
                    os.path.normpath(file_path) in line):
                    # 进一步验证：确保这是一个文件行（包含勾选框）
                    if line.strip().startswith('☐') or line.strip().startswith('☑') or '  - ' in line:
                        target_line = i + 1  # 转换为1基索引
                        break

            if target_line:
                # 滚动到目标行
                self.text_area.see(f"{target_line}.0")

                # 高亮显示该行（临时高亮）
                self.text_area.tag_remove("highlight", "1.0", tk.END)
                self.text_area.tag_add("highlight", f"{target_line}.0", f"{target_line}.end")
                self.text_area.tag_configure("highlight", background="lightblue", foreground="black")

                # 3秒后移除高亮
                self.root.after(3000, lambda: self.text_area.tag_remove("highlight", "1.0", tk.END))

                # 预览该文件
                self.preview_file(file_path)

                # 显示状态信息
                filename = os.path.basename(file_path)
                self.show_status(f"已跳转到文件: {filename}")

            else:
                self.show_status(f"未找到文件在结果中的位置: {os.path.basename(file_path)}")

        except Exception as e:
            print(f"跳转到文件组时出错: {str(e)}")
            self.show_status(f"跳转失败: {str(e)}")

    def remove_single_file(self, file_path, size):
        """从结果列表中移除单个文件（不删除实际文件）"""
        try:
            # 在移出操作前主动停止视频预览以提高性能
            if self.is_playing or self._current_preview:
                print("移出单个文件操作前停止视频预览以提高性能")
                self.stop_preview()

            # 从结果中移除文件
            if size in self.current_results and file_path in self.current_results[size]:
                # 检查当前预览的文件是否属于这个组
                current_preview_in_group = False
                if self._current_preview and size in self.current_results:
                    current_preview_in_group = self._current_preview in self.current_results[size]

                self.current_results[size].remove(file_path)
                # 如果该大小组中的文件少于2个，删除整个大小组
                group_deleted = False
                if len(self.current_results[size]) < 2:
                    del self.current_results[size]
                    group_deleted = True

                # 优化：即使组被删除，也尝试使用缓存恢复（包含组删除处理）
                if not self.load_from_cache_after_delete([file_path]):
                    print(f"缓存恢复失败，使用完全更新")
                    self.display_results(self.current_results)

                # 如果当前预览的文件属于被删除的组，停止预览
                if current_preview_in_group and group_deleted:
                    self.stop_preview()
                    self.show_status("该组已被移除，预览已停止")
                else:
                    self.show_status(f"已从列表中移除文件")
                
                # 更新结果文件
                self.update_result_file()
                
                # 如果没有结果了，禁用相关按钮
                if not self.current_results:
                    self.select_all_btn.configure(state='disabled')
                    self.deselect_all_btn.configure(state='disabled')
                    self.delete_selected_btn.configure(state='disabled')
                    self.remove_from_list_btn.configure(state='disabled')
                    self.save_btn.configure(state='disabled')
                    self.text_area.delete(1.0, tk.END)
                    self.text_area.insert(tk.END, "未找到大小相同的文件\n")
        except Exception as e:
            error_msg = f"移除文件出错: {str(e)}"
            print(error_msg)
            self.show_status(error_msg)

def main():
    root = tk.Tk()
    app = FileSearchApp(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main() 