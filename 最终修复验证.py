#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证删除和移除文件后组自动删除的修复
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_all_operations():
    """测试所有删除和移除操作"""
    print("最终验证删除和移除文件后组自动删除的修复")
    print("=" * 60)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="final_fix_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        # 组1：2个文件（用于测试单个删除）
        group1_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"delete_single_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('A' * 1024)
            group1_files.append(file_path)
        
        # 组2：2个文件（用于测试单个移除）
        group2_files = []
        for i in range(2):
            file_path = os.path.join(test_dir, f"remove_single_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('B' * 2048)
            group2_files.append(file_path)
        
        # 组3：3个文件（用于测试批量删除）
        group3_files = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"delete_batch_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('C' * 4096)
            group3_files.append(file_path)
        
        # 组4：3个文件（用于测试批量移除）
        group4_files = []
        for i in range(3):
            file_path = os.path.join(test_dir, f"remove_batch_{i}.jpg")
            with open(file_path, 'w') as f:
                f.write('D' * 8192)
            group4_files.append(file_path)
        
        print(f"创建了4个测试组，共 {len(group1_files + group2_files + group3_files + group4_files)} 个文件")
        
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        app.current_directory = test_dir
        app.include_images_var.set(True)
        
        # 执行搜索
        print(f"\n执行搜索...")
        app._search_by_size_worker()
        print(f"搜索完成，找到 {len(app.current_results)} 个重复文件组")
        
        initial_groups = len(app.current_results)
        
        # 测试1：单个删除操作
        print(f"\n=== 测试1: 单个删除操作 ===")
        
        if 1024 in app.current_results:
            file_to_delete = group1_files[0]
            print(f"删除文件: {os.path.basename(file_to_delete)}")
            print(f"删除前组1有 {len(app.current_results[1024])} 个文件")
            
            app.delete_file(file_to_delete, 1024)
            
            if 1024 not in app.current_results:
                print("✅ 单个删除：组已被正确删除")
            else:
                print(f"❌ 单个删除：组仍存在，剩余 {len(app.current_results[1024])} 个文件")
        
        # 测试2：单个移除操作
        print(f"\n=== 测试2: 单个移除操作 ===")
        
        if 2048 in app.current_results:
            file_to_remove = group2_files[0]
            print(f"移除文件: {os.path.basename(file_to_remove)}")
            print(f"移除前组2有 {len(app.current_results[2048])} 个文件")
            
            app.remove_single_file(file_to_remove, 2048)
            
            if 2048 not in app.current_results:
                print("✅ 单个移除：组已被正确删除")
            else:
                print(f"❌ 单个移除：组仍存在，剩余 {len(app.current_results[2048])} 个文件")
        
        # 测试3：批量删除操作
        print(f"\n=== 测试3: 批量删除操作 ===")
        
        if 4096 in app.current_results:
            files_to_delete = group3_files[:2]  # 删除2个，剩1个
            print(f"批量删除 {len(files_to_delete)} 个文件")
            print(f"删除前组3有 {len(app.current_results[4096])} 个文件")
            
            # 模拟勾选文件
            for file_path in files_to_delete:
                if file_path not in app.checkbox_vars:
                    app.checkbox_vars[file_path] = tk.BooleanVar()
                app.checkbox_vars[file_path].set(True)
            
            app.delete_selected_files()
            
            if 4096 not in app.current_results:
                print("✅ 批量删除：组已被正确删除")
            else:
                print(f"❌ 批量删除：组仍存在，剩余 {len(app.current_results[4096])} 个文件")
        
        # 测试4：批量移除操作
        print(f"\n=== 测试4: 批量移除操作 ===")
        
        if 8192 in app.current_results:
            files_to_remove = group4_files[:2]  # 移除2个，剩1个
            print(f"批量移除 {len(files_to_remove)} 个文件")
            print(f"移除前组4有 {len(app.current_results[8192])} 个文件")
            
            # 清除之前的勾选状态
            for var in app.checkbox_vars.values():
                var.set(False)
            
            # 模拟勾选文件
            for file_path in files_to_remove:
                if file_path not in app.checkbox_vars:
                    app.checkbox_vars[file_path] = tk.BooleanVar()
                app.checkbox_vars[file_path].set(True)
            
            app.remove_selected_from_list()
            
            if 8192 not in app.current_results:
                print("✅ 批量移除：组已被正确删除")
            else:
                print(f"❌ 批量移除：组仍存在，剩余 {len(app.current_results[8192])} 个文件")
        
        # 最终状态检查
        print(f"\n=== 最终状态检查 ===")
        final_groups = len(app.current_results)
        print(f"初始组数: {initial_groups}")
        print(f"最终组数: {final_groups}")
        
        # 检查是否有单文件组
        single_file_groups = []
        for size, files in app.current_results.items():
            print(f"  大小 {size}: {len(files)} 个文件")
            if len(files) == 1:
                single_file_groups.append(size)
        
        if not single_file_groups:
            print("✅ 没有发现单文件组")
        else:
            print(f"❌ 发现 {len(single_file_groups)} 个单文件组: {single_file_groups}")
        
        # 检查界面显示
        print(f"\n=== 界面显示检查 ===")
        content = app.text_area.get("1.0", tk.END)
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        group_lines = [line for line in lines if "文件大小:" in line]
        
        print(f"界面显示的组数: {len(group_lines)}")
        print(f"数据中的组数: {final_groups}")
        
        if len(group_lines) == final_groups:
            print("✅ 界面显示与数据一致")
        else:
            print("❌ 界面显示与数据不一致")
        
        # 总结测试结果
        print(f"\n=== 测试结果总结 ===")
        
        all_tests_passed = True
        
        # 检查所有组是否正确删除
        if 1024 in app.current_results:
            print("❌ 单个删除测试失败")
            all_tests_passed = False
        
        if 2048 in app.current_results:
            print("❌ 单个移除测试失败")
            all_tests_passed = False
        
        if 4096 in app.current_results:
            print("❌ 批量删除测试失败")
            all_tests_passed = False
        
        if 8192 in app.current_results:
            print("❌ 批量移除测试失败")
            all_tests_passed = False
        
        if single_file_groups:
            print("❌ 存在单文件组")
            all_tests_passed = False
        
        if len(group_lines) != final_groups:
            print("❌ 界面显示不一致")
            all_tests_passed = False
        
        if all_tests_passed:
            print("🎉 所有测试通过！组自动删除功能完全正常")
        else:
            print("⚠️ 部分测试失败，仍存在问题")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

if __name__ == "__main__":
    test_all_operations()
    
    print("\n" + "=" * 60)
    print("🔧 最终修复验证完成")
    print("=" * 60)
    
    print("\n📊 修复内容总结:")
    print("✅ 修复了delete_file方法：正确传递deleted_groups参数")
    print("✅ 修复了remove_single_file方法：正确传递deleted_groups参数")
    print("✅ 修复了delete_selected_files方法：记录并传递deleted_groups")
    print("✅ 修复了remove_selected_from_list方法：记录deleted_groups")
    print("✅ 增强了incremental_update_display方法：处理deleted_groups")
    
    print("\n🎯 修复原理:")
    print("问题: 增量更新时检测不到已删除的组")
    print("原因: 组在检测前就被删除了")
    print("解决: 在删除组前记录组信息，传递给更新方法")
    print("结果: 更新方法能正确识别需要移除的组")
    
    print("\n💡 预期效果:")
    print("- 单个删除文件：组自动删除 ✅")
    print("- 单个移除文件：组自动删除 ✅")
    print("- 批量删除文件：组自动删除 ✅")
    print("- 批量移除文件：组自动删除 ✅")
    print("- 界面实时同步：立即反映变化 ✅")
    print("- 性能保持优秀：使用智能更新策略 ✅")
